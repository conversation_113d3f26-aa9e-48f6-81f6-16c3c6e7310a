<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Meal Plan Diversity</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input { width: 300px; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }
        .meal-plan { border: 1px solid #ccc; margin: 10px 0; padding: 10px; border-radius: 5px; }
        .day { margin: 10px 0; padding: 10px; background: #f9f9f9; border-radius: 3px; }
        .meal-type { margin: 5px 0; }
        .meal-name { font-weight: bold; color: #333; }
        .diversity-stats { background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Meal Plan Diversity</h1>
        
        <div class="section">
            <h3>1. Authentication</h3>
            <input type="text" id="authToken" placeholder="Enter your auth token here">
            <button class="button" onclick="testAuth()">Test Auth</button>
            <div id="authResult"></div>
        </div>

        <div class="section">
            <h3>2. Generate Diverse Meal Plan</h3>
            <button class="button" onclick="generateMealPlan()">Generate 7-Day Meal Plan</button>
            <div id="generateResult"></div>
        </div>

        <div class="section">
            <h3>3. Diversity Analysis</h3>
            <div id="diversityAnalysis"></div>
        </div>

        <div class="section">
            <h3>4. Generated Meal Plans</h3>
            <div id="mealPlansDisplay"></div>
        </div>
    </div>

    <script>
        let generatedPlans = [];

        async function testAuth() {
            const token = document.getElementById('authToken').value;
            const result = document.getElementById('authResult');
            
            if (!token) {
                result.innerHTML = '<div class="error">Please enter an auth token</div>';
                return;
            }
            
            result.innerHTML = '<div class="info">Testing authentication...</div>';
            
            try {
                const response = await fetch('http://localhost:5000/api/users/profile', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    result.innerHTML = `<div class="success">✅ Authentication successful! User: ${data.firstName || 'Unknown'}</div>`;
                } else {
                    result.innerHTML = `<div class="error">❌ Auth failed (${response.status}): ${data.message || 'Unknown error'}</div>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Request failed: ${error.message}</div>`;
            }
        }

        async function generateMealPlan() {
            const token = document.getElementById('authToken').value;
            const result = document.getElementById('generateResult');
            
            if (!token) {
                result.innerHTML = '<div class="error">Please enter an auth token first</div>';
                return;
            }
            
            result.innerHTML = '<div class="info">Generating diverse meal plan...</div>';
            
            // Calculate date range (7 days from today)
            const today = new Date();
            const endDate = new Date(today);
            endDate.setDate(today.getDate() + 6);
            
            const generateData = {
                startDate: formatDate(today),
                endDate: formatDate(endDate),
                includeFamily: true,
                calorieTarget: 2000
            };
            
            try {
                const response = await fetch('http://localhost:5000/api/meal-plans/generate', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(generateData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    generatedPlans = data.plans || [];
                    result.innerHTML = `<div class="success">✅ Generated ${data.generatedPlans} meal plans successfully! Unique meals: ${data.uniqueMealsSelected || 'N/A'}</div>`;
                    displayMealPlans();
                    analyzeDiversity();
                } else {
                    result.innerHTML = `<div class="error">❌ Generation failed (${response.status}): ${data.message || 'Unknown error'}</div>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Request failed: ${error.message}</div>`;
            }
        }

        function formatDate(date) {
            return date.toISOString().split('T')[0];
        }

        function displayMealPlans() {
            const display = document.getElementById('mealPlansDisplay');
            
            if (generatedPlans.length === 0) {
                display.innerHTML = '<div class="info">No meal plans to display</div>';
                return;
            }
            
            let html = '';
            generatedPlans.forEach(plan => {
                html += `
                    <div class="meal-plan">
                        <h4>📅 ${plan.date}</h4>
                        <div class="day">
                            <div class="meal-type">🍳 <strong>Breakfast:</strong> 
                                ${plan.breakfast.map(meal => `<span class="meal-name">${meal.name}</span>`).join(', ') || 'None'}
                            </div>
                            <div class="meal-type">🍽️ <strong>Lunch:</strong> 
                                ${plan.lunch.map(meal => `<span class="meal-name">${meal.name}</span>`).join(', ') || 'None'}
                            </div>
                            <div class="meal-type">🍖 <strong>Dinner:</strong> 
                                ${plan.dinner.map(meal => `<span class="meal-name">${meal.name}</span>`).join(', ') || 'None'}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            display.innerHTML = html;
        }

        function analyzeDiversity() {
            const analysis = document.getElementById('diversityAnalysis');
            
            if (generatedPlans.length === 0) {
                analysis.innerHTML = '<div class="info">No meal plans to analyze</div>';
                return;
            }
            
            // Collect all meals
            const allMeals = [];
            const mealsByType = { breakfast: [], lunch: [], dinner: [] };
            
            generatedPlans.forEach(plan => {
                ['breakfast', 'lunch', 'dinner'].forEach(mealType => {
                    plan[mealType].forEach(meal => {
                        allMeals.push(meal.name);
                        mealsByType[mealType].push(meal.name);
                    });
                });
            });
            
            // Calculate diversity stats
            const uniqueMeals = [...new Set(allMeals)];
            const totalMeals = allMeals.length;
            const diversityPercentage = ((uniqueMeals.length / totalMeals) * 100).toFixed(1);
            
            // Find duplicates
            const mealCounts = {};
            allMeals.forEach(meal => {
                mealCounts[meal] = (mealCounts[meal] || 0) + 1;
            });
            
            const duplicates = Object.entries(mealCounts).filter(([meal, count]) => count > 1);
            
            let html = `
                <div class="diversity-stats">
                    <h4>📊 Diversity Analysis</h4>
                    <p><strong>Total Meals Generated:</strong> ${totalMeals}</p>
                    <p><strong>Unique Meals:</strong> ${uniqueMeals.length}</p>
                    <p><strong>Diversity Score:</strong> ${diversityPercentage}% (Higher is better)</p>
                    <p><strong>Expected for Perfect Diversity:</strong> ${generatedPlans.length * 3} unique meals</p>
                </div>
            `;
            
            if (duplicates.length > 0) {
                html += `
                    <div class="error">
                        <h4>🔄 Duplicate Meals Found:</h4>
                        <ul>
                            ${duplicates.map(([meal, count]) => `<li>${meal} (appears ${count} times)</li>`).join('')}
                        </ul>
                    </div>
                `;
            } else {
                html += `<div class="success"><h4>✅ Perfect Diversity! No duplicate meals found.</h4></div>`;
            }
            
            // Analyze by meal type
            html += '<div class="info"><h4>📋 Meals by Type:</h4>';
            ['breakfast', 'lunch', 'dinner'].forEach(mealType => {
                const typeMeals = mealsByType[mealType];
                const uniqueTypeMeals = [...new Set(typeMeals)];
                html += `<p><strong>${mealType.charAt(0).toUpperCase() + mealType.slice(1)}:</strong> ${uniqueTypeMeals.length}/${typeMeals.length} unique (${typeMeals.join(', ')})</p>`;
            });
            html += '</div>';
            
            analysis.innerHTML = html;
        }
    </script>
</body>
</html>
