import React, { createContext, useState, useContext, useEffect } from 'react';

const AdminViewContext = createContext();

export const useAdminView = () => {
  const context = useContext(AdminViewContext);
  if (!context) {
    throw new Error('useAdminView must be used within an AdminViewProvider');
  }
  return context;
};

export const AdminViewProvider = ({ children }) => {
  // State to track if admin is viewing as user
  // false = admin view (default), true = user view
  const [isViewingAsUser, setIsViewingAsUser] = useState(false);
  
  // Function to toggle between admin and user view
  const toggleViewMode = () => {
    setIsViewingAsUser(prev => {
      const newValue = !prev;
      console.log('Admin view toggle:', prev ? 'User -> Admin' : 'Admin -> User');
      return newValue;
    });
  };
  
  // Function to explicitly set admin view
  const setAdminView = () => {
    setIsViewingAsUser(false);
  };
  
  // Function to explicitly set user view
  const setUserView = () => {
    setIsViewingAsUser(true);
  };

  // Function to reset to user view on login (for admins)
  const resetToUserViewOnLogin = () => {
    setIsViewingAsUser(true);
    console.log('🔄 Admin view reset to user view on login');
  };

  // Reset to user view when user logs in or logs out
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      // User logged out - reset to admin view (default)
      setIsViewingAsUser(false);
    } else {
      // User logged in - always start with user view
      setIsViewingAsUser(true);
    }
  }, []);

  const value = {
    isViewingAsUser,
    toggleViewMode,
    setAdminView,
    setUserView,
    resetToUserViewOnLogin,
  };

  return (
    <AdminViewContext.Provider value={value}>
      {children}
    </AdminViewContext.Provider>
  );
};
