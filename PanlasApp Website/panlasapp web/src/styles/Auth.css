/* Modern Authentication UI with Enhanced Design */

/* Container */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background:  #48e1c1;
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  padding: 20px;
  font-family: 'Roboto', 'Segoe UI', <PERSON>l, sans-serif;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Auth Box */
.auth-box {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15),
              0 1px 5px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
  padding: 40px;
  /* Removed transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1); */
  position: relative;
  overflow: hidden;
}

.auth-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background:  #48e1c1;
}

/* Removed hover animation to prevent field sizing issues */
/* .auth-box:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2),
              0 2px 10px rgba(0, 0, 0, 0.1);
} */

/* Heading */
.heading {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 35px;
  text-align: center;
  position: relative;
}

.heading:after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background:  #70e4c4;
  border-radius: 2px;
}

/* Form */
.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Input Group */
.input-group {
  position: relative;
  margin-bottom: 20px;
}

.input-group:last-child {
  margin-bottom: 0;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #546e7a;
  transition: color 0.3s;
}

.input-group:focus-within label {
  color: #1890ff;
}

.input {
  width: 100% !important;
  padding: 14px 18px !important;
  border: 2px solid #e0e0e0 !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  /* Removed transition: all 0.3s; */
  background-color: #f9f9f9 !important;
  /* Prevent any transitions or animations */
  transition: none !important;
  transform: none !important;
  /* Ensure consistent box model */
  box-sizing: border-box !important;
  /* Prevent height changes */
  min-height: auto !important;
  max-height: none !important;
  height: auto !important;
}

/* Center align text for password inputs */
.input[type="password"] {
  text-align: center !important;
}

.input:hover:not(:focus):not(.error) {
  border-color: #bdbdbd;
}

.input:focus {
  border-color: #48e1c1;
  /* Reduced box-shadow to prevent field expansion */
  box-shadow: 0 0 0 1px rgba(110, 142, 251, 0.2);
  outline: none;
  background-color: #fff;
}

.input.error {
  border-color: #ff3860 !important;
  background-color: #fff0f3 !important;
  /* Removed box-shadow to prevent field expansion */
  /* Ensure no size changes */
  padding: 14px 18px !important;
  font-size: 16px !important;
  transition: none !important;
  transform: none !important;
  height: auto !important;
}

.input.error:focus {
  border-color: #ff3860 !important;
  /* Removed box-shadow to prevent field expansion */
  background-color: #fff !important;
  /* Ensure no size changes */
  padding: 14px 18px !important;
  font-size: 16px !important;
  transition: none !important;
  transform: none !important;
  height: auto !important;
}

/* Error Message */
.error-message {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  border-left: 4px solid #f44336;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(244, 67, 54, 0.1);
  /* Removed animation: slideInError 0.3s ease-out; */
}

.error-message::before {
  content: "⚠️";
  margin-right: 10px;
  font-size: 16px;
  flex-shrink: 0;
}

.validation-message {
  font-size: 12px;
  margin-top: 6px;
  color: #ff3860;
  display: flex;
  align-items: center;
  /* Removed animation: slideInError 0.3s ease-out; */
  line-height: 1.4;
}

.validation-message::before {
  content: "⚠";
  margin-right: 5px;
  flex-shrink: 0;
}

/* Auth Button */
.auth-button {
  background:  #48e1c1;
  color: white;
  border: none;
  padding: 14px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  margin-top: 15px;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(110, 142, 251, 0.3);
}

.auth-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.auth-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(110, 142, 251, 0.4);
}

.auth-button:hover::after {
  transform: translateX(0);
}

.auth-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(110, 142, 251, 0.3);
}

.auth-button:disabled {
  background: #d1d1d1;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Auth Link */
.auth-link {
  margin-top: 30px;
  text-align: center;
  font-size: 15px;
  color: #78909c;
  position: relative;
  padding-top: 20px;
}

.auth-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(189, 189, 189, 0.5), transparent);
}

.auth-link a {
  color: #2bdfac;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s;
  position: relative;
}

.auth-link a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #2bdfac;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.auth-link a:hover {
  color: #2bdfac;
}

.auth-link a:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Forgot Password */
.forgot-password {
  text-align: right;
  margin-bottom: 8px;
}

.forgot-password a {
  color: #78909c;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s;
  position: relative;
}

.forgot-password a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #78909c;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.forgot-password a:hover {
  color: #6e8efb;
}

.forgot-password a:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Password Strength Indicator */
.password-strength-container {
  margin-top: -5px;
  margin-bottom: 10px;
}

.password-strength-bar {
  height: 6px;
  border-radius: 10px;
  margin-bottom: 8px;
  transition: all 0.4s;
  background-color: #e0e0e0;
  overflow: hidden;
  position: relative;
}

.password-strength-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 10px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.strength-weak .password-strength-bar::before {
  background-color: #ff3860;
  width: 30%;
}

.strength-moderate .password-strength-bar::before {
  background-color: #ffdd57;
  width: 60%;
}

.strength-strong .password-strength-bar::before {
  background-color: #23d160;
  width: 100%;
}

.password-strength {
  font-size: 12px;
  display: flex;
  justify-content: space-between;
  font-weight: 500;
}

.text-weak {
  color: #ff3860;
}

.text-moderate {
  color: #ff9800;
}

.text-strong {
  color: #23d160;
}

/* Form Grid for Signup Form */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-grid .full-width {
  grid-column: span 2;
}

/* Input with Icons */
.input-with-icon {
  position: relative;
}

.input-with-icon .input {
  padding-left: 45px;
}

.input-with-icon .icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #9e9e9e;
  transition: color 0.3s;
}

.input-with-icon:focus-within .icon {
  color: #1890ff;
}

/* Password Input with Toggle */
.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input-wrapper .input {
  padding-right: 45px !important;
}

.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #9e9e9e;
  cursor: pointer;
  font-size: 18px;
  transition: color 0.3s;
  z-index: 2;
}

.password-toggle:hover {
  color: #48e1c1;
}

.password-toggle:focus {
  outline: none;
  color: #48e1c1;
}

/* Social Login Buttons */
.social-login {
  margin-top: 25px;
  margin-bottom: 10px;
  position: relative;
  text-align: center;
}

.social-login::before {
  content: 'or';
  display: inline-block;
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: white;
  padding: 0 15px;
  color: #9e9e9e;
  font-size: 14px;
}

.social-login::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(189, 189, 189, 0.5), transparent);
}

.social-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 25px;
}

.social-button {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.social-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.social-button.google {
  background-color: white;
  color: #DB4437;
}

.social-button.facebook {
  background-color: #4267B2;
  color: white;
}

.social-button.apple {
  background-color: #000;
  color: white;
}

/* Animation for Form Transition */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* .form {
  Removed animation: fadeIn 0.5s ease-out;
} */

/* Form Loader */
.form-loader {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.loader {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(110, 142, 251, 0.2);
  border-radius: 50%;
  border-top-color: #6e8efb;
  animation: circleSpinner 1s ease-in-out infinite;
}

/* Use consistent circle loader for auth forms */
.auth-circle-loader {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(110, 142, 251, 0.2);
  border-radius: 50%;
  border-top-color: #6e8efb;
  animation: circleSpinner 1s ease-in-out infinite;
}

@keyframes circleSpinner {
  to { transform: rotate(360deg); }
}

/* Terms and Conditions Modal */
.terms-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.terms-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
  position: relative;
}

.terms-modal-header {
  background: #48e1c1;
  color: white;
  padding: 24px;
  text-align: center;
  position: relative;
}

.terms-modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
}

.terms-modal-header p {
  margin: 8px 0 0 0;
  opacity: 0.9;
  font-size: 16px;
}

.terms-modal-content {
  padding: 24px;
  max-height: 50vh;
  overflow-y: auto;
  line-height: 1.6;
  color: #333;
}

.terms-modal-content h3 {
  color: #2c3e50;
  margin: 20px 0 12px 0;
  font-size: 18px;
  font-weight: 600;
}

.terms-modal-content p {
  margin-bottom: 16px;
  color: #546e7a;
}

.terms-modal-content ul {
  margin: 12px 0;
  padding-left: 20px;
}

.terms-modal-content li {
  margin-bottom: 8px;
  color: #546e7a;
}

.terms-modal-actions {
  padding: 24px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  gap: 16px;
  justify-content: flex-end;
}

.terms-modal-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  min-width: 120px;
}

.terms-modal-button.decline {
  background-color: #f5f5f5;
  color: #666;
}

.terms-modal-button.decline:hover {
  background-color: #e0e0e0;
}

.terms-modal-button.accept {
  background:  #48e1c1;
  color: white;
  box-shadow: 0 4px 15px rgba(72, 225, 193, 0.3);
}

.terms-modal-button.accept:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(72, 225, 193, 0.4);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Styles */
@media (max-width: 600px) {
  .auth-box {
    padding: 30px 20px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-grid .full-width {
    grid-column: span 1;
  }

  .heading {
    font-size: 28px;
  }

  .terms-modal {
    width: 95%;
    max-height: 90vh;
  }

  .terms-modal-header {
    padding: 20px;
  }

  .terms-modal-header h2 {
    font-size: 20px;
  }

  .terms-modal-content {
    padding: 20px;
  }

  .terms-modal-actions {
    padding: 20px;
    flex-direction: column;
  }

  .terms-modal-button {
    width: 100%;
  }
}

/* Focus Visible for Accessibility */
:focus-visible {
  outline: 3px solid rgba(110, 142, 251, 0.5);
  outline-offset: 2px;
}

/* Additional Animations for Input Focus - REMOVED */
/* .input-group {
  transition: transform 0.3s;
}

.input-group:focus-within {
  transform: translateY(-2px);
} */

/* Custom Checkbox Styling */
.checkbox-group {
  display: flex;
  align-items: center;
  margin-top: 5px;
}

.checkbox-group input[type="checkbox"] {
  position: absolute;
  opacity: 0;
}

.checkbox-group label {
  position: relative;
  padding-left: 35px;
  cursor: pointer;
  font-size: 14px;
  color: #546e7a;
}

.checkbox-group label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
  transition: all 0.3s;
}

.checkbox-group input[type="checkbox"]:checked + label::before {
  background-color: #1890ff;
  border-color: #1890ff;
}

.checkbox-group input[type="checkbox"]:checked + label::after {
  content: '✓';
  position: absolute;
  left: 5px;
  top: 1px;
  color: white;
  font-size: 14px;
}

.checkbox-group input[type="checkbox"]:focus + label::before {
  box-shadow: 0 0 0 3px rgba(110, 142, 251, 0.2);
}

/* Email Validator Styles */
.email-validator {
  margin-top: 8px;
  font-size: 12px;
}

.email-validator.valid {
  color: #4CAF50;
  display: flex;
  align-items: center;
  gap: 5px;
}

.email-validator.invalid {
  color: #FF5252;
}

.email-validator .error-item {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 4px;
}

.email-validator .error-item:last-child {
  margin-bottom: 0;
}

/* Password Strength Indicator Styles */
.password-strength-indicator {
  margin-top: 8px;
}

.password-strength-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.password-strength-label {
  color: #546e7a;
  font-weight: 500;
}

.password-strength-value {
  font-weight: 600;
}

.password-strength-value.weak {
  color: #FF5252;
}

.password-strength-value.medium {
  color: #FF9800;
}

.password-strength-value.strong {
  color: #4CAF50;
}

.password-progress-container {
  margin-bottom: 8px;
}

.password-progress-bar {
  width: 100%;
  height: 6px;
  background-color: #E0E0E0;
  border-radius: 3px;
  overflow: hidden;
}

.password-progress-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 3px;
}

.password-feedback {
  font-size: 11px;
  color: #78909c;
}

.password-feedback-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.password-feedback-item {
  margin-bottom: 2px;
}

/* OTP Verification Styles */
.otp-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.otp-header {
  text-align: center;
}

.otp-icon {
  font-size: 60px;
  color: #70e4c4;
  margin-bottom: 20px;
}

.otp-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
}

.otp-subtitle {
  font-size: 16px;
  color: #78909c;
  margin-bottom: 8px;
}

.otp-email {
  font-size: 16px;
  font-weight: 600;
  color: #70e4c4;
}

.otp-input-container {
  display: flex;
  gap: 12px;
  margin-bottom: 30px;
}

.otp-input {
  width: 50px;
  height: 50px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  transition: all 0.3s;
  background-color: #f9f9f9;
}

.otp-input:focus {
  border-color: #70e4c4;
  box-shadow: 0 0 0 3px rgba(112, 228, 196, 0.2);
  outline: none;
  background-color: #fff;
}

.otp-input.filled {
  border-color: #70e4c4;
  background-color: #fff;
  color: #2c3e50;
}

.otp-verify-button {
  background: linear-gradient(to right, #70e4c4, #2bdfac);
  color: white;
  border: none;
  padding: 14px 40px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(112, 228, 196, 0.3);
}

.otp-verify-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(112, 228, 196, 0.4);
}

.otp-verify-button:disabled {
  background: linear-gradient(to right, #d1d1d1, #c8c8c8);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.otp-resend-container {
  text-align: center;
  margin-top: 20px;
}

.otp-resend-text {
  font-size: 14px;
  color: #78909c;
  margin-bottom: 8px;
}

.otp-resend-button {
  background: none;
  border: none;
  color: #70e4c4;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.3s;
}

.otp-resend-button:hover:not(:disabled) {
  color: #2bdfac;
}

.otp-resend-button:disabled {
  color: #bdbdbd;
  cursor: not-allowed;
  text-decoration: none;
}

.otp-back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  background: none;
  border: none;
  font-size: 24px;
  color: #78909c;
  cursor: pointer;
  transition: color 0.3s;
}

.otp-back-button:hover {
  color: #2c3e50;
}