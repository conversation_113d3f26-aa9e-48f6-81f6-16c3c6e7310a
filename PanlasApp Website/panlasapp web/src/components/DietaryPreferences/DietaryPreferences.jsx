import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import userAPI from '../../services/userAPI';
import aiAPI from '../../services/aiAPI';
import Header from '../Header/Header';
import Sidebar from '../Sidebar/Sidebar';
import Swal from 'sweetalert2';
import './DietaryPreferences.css';

const DietaryPreferences = () => {
  const [preferences, setPreferences] = useState({
    restrictions: [],
    allergies: [],
    dislikedIngredients: [],
    calorieTarget: '',
    mealFrequency: 3
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [sidebarActive, setSidebarActive] = useState(false);
  const [conflicts, setConflicts] = useState(null);
  const [checkingConflicts, setCheckingConflicts] = useState(false);
  const [familyMembers, setFamilyMembers] = useState([]);
  const [customAllergy, setCustomAllergy] = useState('');
  const [showCustomAllergyInput, setShowCustomAllergyInput] = useState(false);
  const [customRestriction, setCustomRestriction] = useState('');
  const [showCustomRestrictionInput, setShowCustomRestrictionInput] = useState(false);

  const navigate = useNavigate();

  // Dietary restrictions options (matching mobile app and backend)
  const dietaryOptions = [
    'Vegetarian',
    'Vegan',
    'Dairy-Free',
    'Egg-Free',
    'Gluten-Free',
    'Soy-Free',
    'Nut-Free',
    'Low-Carb',
    'Low-Sugar',
    'Sugar-Free',
    'Low-Fat',
    'Low-Sodium',
    'Organic',
    'Halal',
    'High-Protein',
    'Pescatarian',
    'Keto',
    'Plant-Based',
    'Kosher',
    'Climatarian',
    'Raw Food',
    'Mediterranean',
    'Paleo',
    'Kangatarian',
    'Pollotarian',
    'Flexitarian',
    'None'
  ];

  // Allergy options (matching mobile app and backend)
  const allergyOptions = [
    'Milk',
    'Eggs',
    'Fish',
    'Shellfish',
    'Peanuts',
    'Tree Nuts',
    'Wheat',
    'Soybeans',
    'Sesame',
    'Mustard',
    'Celery',
    'Lupin',
    'Mollusks',
    'Sulfites',
    'None'
  ];

  // Migration mapping for old allergy names to new standardized names
  const allergyMigrationMap = {
    'Nuts': 'Tree Nuts',
    'Gluten': 'Wheat',
    'Soy': 'Soybeans',
    'Dairy': 'Milk'
  };

  // Function to migrate old allergy names to new ones
  const migrateAllergies = (allergies) => {
    return allergies.map(allergy => allergyMigrationMap[allergy] || allergy);
  };

  // Check if there are custom allergies and set the "Other" option accordingly
  useEffect(() => {
    const customAllergies = preferences.allergies.filter(allergy => !allergyOptions.includes(allergy));
    if (customAllergies.length > 0) {
      setShowCustomAllergyInput(true);
      setCustomAllergy(customAllergies.join(', ')); // Show all custom allergies in the text field
    }
  }, [preferences.allergies]);

  // Check if there are custom restrictions and set the "Other" option accordingly
  useEffect(() => {
    const customRestrictions = preferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction));
    if (customRestrictions.length > 0) {
      setShowCustomRestrictionInput(true);
      setCustomRestriction(customRestrictions.join(', ')); // Show all custom restrictions in the text field
    }
  }, [preferences.restrictions]);

  useEffect(() => {
    loadPreferences();
    loadFamilyMembers();
  }, []);

  const loadFamilyMembers = async () => {
    try {
      const response = await userAPI.getFamilyMembers();
      setFamilyMembers(response.data || []);
    } catch (error) {
      console.error('Error loading family members:', error);
      setFamilyMembers([]);
    }
  };

  const loadPreferences = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await userAPI.getDietaryPreferences();
      console.log('Loaded preferences:', response);
      
      if (response.success && response.dietaryPreferences) {
        // Migrate old allergy names to new standardized names
        const migratedAllergies = migrateAllergies(response.dietaryPreferences.allergies || []);

        setPreferences({
          restrictions: response.dietaryPreferences.restrictions || [],
          allergies: migratedAllergies,
          dislikedIngredients: response.dietaryPreferences.dislikedIngredients || [],
          calorieTarget: response.dietaryPreferences.calorieTarget?.toString() || '',
          mealFrequency: response.dietaryPreferences.mealFrequency || 3
        });
      }
    } catch (error) {
      console.error('Error loading preferences:', error);
      setError('Failed to load dietary preferences');
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async () => {
    try {
      setSaving(true);
      console.log('Saving preferences..');
      setError('');
      setSuccess('');

      // Check if at least one dietary preference is selected (restrictions or allergies)
      const hasRestrictions = preferences.restrictions.length > 0;
      const hasAllergies = preferences.allergies.length > 0;

      if (!hasRestrictions && !hasAllergies) {
        setError('Please select at least one dietary restriction or allergy. This helps us provide better meal recommendations.');
        setSaving(false);
        return;
      }

      const preferencesToSave = {
        restrictions: preferences.restrictions,
        allergies: preferences.allergies,
        dislikedIngredients: preferences.dislikedIngredients,
        calorieTarget: preferences.calorieTarget ? parseInt(preferences.calorieTarget) : null,
        mealFrequency: preferences.mealFrequency
      };

      console.log('Saving preferences:', preferencesToSave);

      const response = await userAPI.updateDietaryPreferences(preferencesToSave);
      console.log('Save response:', response);

      if (response.success) {

        // Show success message with SweetAlert2
        let successMessage = 'Your dietary preferences have been saved successfully!';
        let additionalInfo = '';

        if (response.mealPlanUpdateScheduled) {
          additionalInfo = '\n\n🤖 AI is working in the background to update your future meal plans to match your new preferences.';
        }

        await Swal.fire({
          icon: 'success',
          title: 'Preferences Saved!',
          text: successMessage + additionalInfo,
          confirmButtonText: 'Great!',
          confirmButtonColor: '#28a745',
          timer: 5000,
          timerProgressBar: true,
          showClass: {
            popup: 'animate__animated animate__fadeInDown'
          },
          hideClass: {
            popup: 'animate__animated animate__fadeOutUp'
          }
        });

        // Also set the success state for any other UI elements
        setSuccess('Dietary preferences saved successfully!');
        setTimeout(() => setSuccess(''), 5000);
      } else {
        setError(response.error || 'Failed to save preferences');

        // Show error with SweetAlert2
        await Swal.fire({
          icon: 'error',
          title: 'Save Failed',
          text: response.error || 'Failed to save preferences. Please try again.',
          confirmButtonText: 'OK',
          confirmButtonColor: '#dc3545'
        });
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
      setError('Failed to save preferences');

      // Show error with SweetAlert2
      await Swal.fire({
        icon: 'error',
        title: 'Connection Error',
        text: 'Failed to save preferences. Please check your connection and try again.',
        confirmButtonText: 'OK',
        confirmButtonColor: '#dc3545'
      });
    } finally {
      setSaving(false);
    }
  };

  const toggleRestriction = (restriction) => {
    setPreferences(prev => {
      if (restriction === 'None') {
        // If "None" is clicked, clear all other restrictions and set only "None"
        return {
          ...prev,
          restrictions: prev.restrictions.includes('None') ? [] : ['None']
        };
      } else {
        // If any other restriction is clicked, remove "None" if it exists
        const newRestrictions = prev.restrictions.includes(restriction)
          ? prev.restrictions.filter(r => r !== restriction)
          : [...prev.restrictions.filter(r => r !== 'None'), restriction];

        return {
          ...prev,
          restrictions: newRestrictions
        };
      }
    });
  };

  const toggleAllergy = (allergy) => {
    setPreferences(prev => {
      if (allergy === 'None') {
        // If "None" is clicked, clear all other allergies and set only "None"
        return {
          ...prev,
          allergies: prev.allergies.includes('None') ? [] : ['None']
        };
      } else {
        // If any other allergy is clicked, remove "None" if it exists
        const newAllergies = prev.allergies.includes(allergy)
          ? prev.allergies.filter(a => a !== allergy)
          : [...prev.allergies.filter(a => a !== 'None'), allergy];

        return {
          ...prev,
          allergies: newAllergies
        };
      }
    });
  };

  const addCustomAllergy = () => {
    if (customAllergy.trim()) {
      // Split by comma and add multiple allergies
      const newAllergies = customAllergy.split(',').map(a => a.trim()).filter(a => a && !preferences.allergies.includes(a));
      if (newAllergies.length > 0) {
        setPreferences(prev => ({
          ...prev,
          allergies: [...prev.allergies, ...newAllergies]
        }));
        setCustomAllergy('');
        setShowCustomAllergyInput(false);
      }
    }
  };

  const addCustomRestriction = () => {
    if (customRestriction.trim()) {
      // Split by comma and add multiple restrictions
      const newRestrictions = customRestriction.split(',').map(r => r.trim()).filter(r => r && !preferences.restrictions.includes(r));
      if (newRestrictions.length > 0) {
        setPreferences(prev => ({
          ...prev,
          restrictions: [...prev.restrictions, ...newRestrictions]
        }));
        setCustomRestriction('');
        setShowCustomRestrictionInput(false);
      }
    }
  };

  const removeCustomAllergy = (allergy) => {
    setPreferences(prev => {
      const updatedAllergies = prev.allergies.filter(a => a !== allergy);
      const customAllergies = updatedAllergies.filter(a => !allergyOptions.includes(a));

      // Update the custom allergy text field
      setCustomAllergy(customAllergies.join(', '));

      // Hide the input if no custom allergies remain
      if (customAllergies.length === 0) {
        setShowCustomAllergyInput(false);
      }

      return {
        ...prev,
        allergies: updatedAllergies
      };
    });
  };

  const removeCustomRestriction = (restriction) => {
    setPreferences(prev => {
      const updatedRestrictions = prev.restrictions.filter(r => r !== restriction);
      const customRestrictions = updatedRestrictions.filter(r => !dietaryOptions.includes(r));

      // Update the custom restriction text field
      setCustomRestriction(customRestrictions.join(', '));

      // Hide the input if no custom restrictions remain
      if (customRestrictions.length === 0) {
        setShowCustomRestrictionInput(false);
      }

      return {
        ...prev,
        restrictions: updatedRestrictions
      };
    });
  };

  const addDislikedIngredient = (ingredient) => {
    if (ingredient.trim() && !preferences.dislikedIngredients.includes(ingredient.trim())) {
      setPreferences(prev => ({
        ...prev,
        dislikedIngredients: [...prev.dislikedIngredients, ingredient.trim()]
      }));
    }
  };

  const removeDislikedIngredient = (ingredient) => {
    setPreferences(prev => ({
      ...prev,
      dislikedIngredients: prev.dislikedIngredients.filter(i => i !== ingredient)
    }));
  };

  const handleDislikedIngredientKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addDislikedIngredient(e.target.value);
      e.target.value = '';
    }
  };

  const toggleSidebar = () => {
    setSidebarActive(!sidebarActive);
  };

  // Check for dietary conflicts using AI
  const checkDietaryConflicts = async (currentPreferences) => {
    try {
      setCheckingConflicts(true);

      // Prepare family member preferences for conflict checking
      const familyPreferences = familyMembers.map(member => ({
        name: member.name,
        restrictions: member.dietaryPreferences?.restrictions || [],
        allergies: member.dietaryPreferences?.allergies || [],
        dislikedIngredients: member.dietaryPreferences?.dislikedIngredients || []
      }));

      const response = await aiAPI.detectDietaryConflicts({
        userPreferences: {
          restrictions: currentPreferences.restrictions,
          allergies: currentPreferences.allergies,
          dislikedIngredients: currentPreferences.dislikedIngredients
        },
        familyMembers: familyPreferences
      });

      if (response.success && response.conflicts) {
        setConflicts(response.conflicts);
      }
    } catch (error) {
      console.error('Error checking dietary conflicts:', error);
      // Don't show error to user, just log it
    } finally {
      setCheckingConflicts(false);
    }
  };

  // Check for conflicts whenever preferences change
  useEffect(() => {
    if (preferences.restrictions.length > 0 || preferences.allergies.length > 0) {
      const timeoutId = setTimeout(() => {
        checkDietaryConflicts(preferences);
      }, 1000); // Debounce for 1 second

      return () => clearTimeout(timeoutId);
    } else {
      setConflicts(null);
    }
  }, [preferences.restrictions, preferences.allergies, preferences.dislikedIngredients]);

  if (loading) {
    return (
      <div className="app-container">
        <Header toggleSidebar={toggleSidebar} />
        <Sidebar isActive={sidebarActive} />
        <div className="content-area">
          <div className="main-content">
            <div className="loading-state">
              <div className="circle-loader large primary"></div>
              <p>Loading dietary preferences...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="app-container">
      <Header toggleSidebar={toggleSidebar} />
      <Sidebar isActive={sidebarActive} />
      <div className="content-area">
        <div className="main-content">
          <div className="dietary-preferences-container">
            <div className="dietary-preferences-header">
              <button 
                className="back-button"
                onClick={() => navigate('/profile')}
              >
                ← Back to Family Profile
              </button>
              <h1>Family Dietary Preferences</h1>
              <p>Select dietary restrictions, allergies, and preferences applicable to each family member.</p>
            </div>

            {error && <div className="error-message">{error}</div>}
            {success && <div className="success-message">{success}</div>}

            <div className="preferences-form">
              {/* Dietary Restrictions */}
              <div className="preference-section">
                <h3>Dietary Restrictions</h3>
                <p>Select any dietary restrictions for all family members:</p>
                <p className="learn-more-link">
                  Don't know what Dietary preferences mean? <a href="/help-center" target="_blank" rel="noopener noreferrer">Learn more</a>
                </p>
                <div className="options-grid">
                  {dietaryOptions.map((option) => (
                    <label key={option} className="option-checkbox">
                      <input
                        type="checkbox"
                        checked={preferences.restrictions.includes(option)}
                        onChange={() => toggleRestriction(option)}
                      />
                      <span className="checkmark"></span>
                      {option}
                    </label>
                  ))}
                  {/* Other option */}
                  <label className="option-checkbox">
                    <input
                      type="checkbox"
                      checked={showCustomRestrictionInput || preferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction)).length > 0}
                      onChange={() => setShowCustomRestrictionInput(!showCustomRestrictionInput)}
                    />
                    <span className="checkmark"></span>
                    Other
                  </label>
                </div>

                {/* Custom restriction input */}
                {showCustomRestrictionInput && (
                  <div className="custom-input-container">
                    <input
                      type="text"
                      className="custom-input"
                      placeholder="Enter custom dietary restriction"
                      value={customRestriction}
                      onChange={(e) => setCustomRestriction(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addCustomRestriction()}
                    />
                    <button
                      type="button"
                      className="add-button"
                      onClick={addCustomRestriction}
                      disabled={!customRestriction.trim()}
                    >
                      Add
                    </button>
                  </div>
                )}

                {/* Display custom restrictions */}
                {preferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction)).length > 0 && (
                  <div className="custom-items-display">
                    <h4>Custom Dietary Restrictions:</h4>
                    <div className="custom-items-list">
                      {preferences.restrictions
                        .filter(restriction => !dietaryOptions.includes(restriction))
                        .map(restriction => (
                          <div key={restriction} className="custom-restriction-item">
                            <span>{restriction}</span>
                            <button
                              type="button"
                              className="remove-custom-restriction"
                              onClick={() => removeCustomRestriction(restriction)}
                              title="Remove restriction"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Allergies */}
              <div className="preference-section">
                <h3>Allergies</h3>
                <p>Select any food allergies that apply to each family member:</p>
                <div className="options-grid">
                  {allergyOptions.map((option) => (
                    <label key={option} className="option-checkbox">
                      <input
                        type="checkbox"
                        checked={preferences.allergies.includes(option)}
                        onChange={() => toggleAllergy(option)}
                      />
                      <span className="checkmark"></span>
                      {option}
                    </label>
                  ))}
                  {/* Other option */}
                  <label className="option-checkbox">
                    <input
                      type="checkbox"
                      checked={showCustomAllergyInput || preferences.allergies.filter(allergy => !allergyOptions.includes(allergy)).length > 0}
                      onChange={() => setShowCustomAllergyInput(!showCustomAllergyInput)}
                    />
                    <span className="checkmark"></span>
                    Other
                  </label>
                </div>

                {/* Custom allergy input */}
                {showCustomAllergyInput && (
                  <div className="custom-input-container">
                    <input
                      type="text"
                      className="custom-input"
                      placeholder="Enter custom allergy"
                      value={customAllergy}
                      onChange={(e) => setCustomAllergy(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addCustomAllergy()}
                    />
                    <button
                      type="button"
                      className="add-button"
                      onClick={addCustomAllergy}
                      disabled={!customAllergy.trim()}
                    >
                      Add
                    </button>
                  </div>
                )}

                {/* Display custom allergies */}
                {preferences.allergies.filter(allergy => !allergyOptions.includes(allergy)).length > 0 && (
                  <div className="custom-allergies-container">
                    <h4>Custom Allergies:</h4>
                    <div className="custom-allergies-list">
                      {preferences.allergies
                        .filter(allergy => !allergyOptions.includes(allergy))
                        .map(allergy => (
                          <div key={allergy} className="custom-allergy-item">
                            <span>{allergy}</span>
                            <button
                              type="button"
                              className="remove-custom-allergy"
                              onClick={() => removeCustomAllergy(allergy)}
                              title="Remove allergy"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>

              {/* AI Conflict Detection Warning */}
              {conflicts && conflicts.hasConflicts && (
                <div className="conflict-warning-section">
                  <div className="conflict-header">
                    <span className="warning-icon">⚠️</span>
                    <h3>Dietary Conflicts Detected</h3>
                  </div>
                  {conflicts.conflicts.map((conflict, index) => (
                    <div key={index} className="conflict-item">
                      <div className="conflict-text">
                        <strong className="conflict-items">
                          {conflict.items.join(' + ')}
                        </strong>
                        {conflict.type === 'family' && (
                          <span className="conflict-type"> (Family Conflict)</span>
                        )}
                        {conflict.type === 'user-family' && (
                          <span className="conflict-type"> (User vs Family)</span>
                        )}
                        : {conflict.reason}
                        {conflict.severity === 'high' && (
                          <span className="severity-badge high">High Priority</span>
                        )}
                      </div>
                    </div>
                  ))}
                  {conflicts.suggestions && conflicts.suggestions.length > 0 && (
                    <div className="suggestions-container">
                      <h4>AI Suggestions:</h4>
                      <ul className="suggestions-list">
                        {conflicts.suggestions.map((suggestion, index) => (
                          <li key={index}>{suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  <p className="conflict-note">
                    Note: You can still save these preferences, but consider the conflicts above.
                  </p>
                </div>
              )}

              {/* Loading indicator for conflict checking */}
              {checkingConflicts && (
                <div className="checking-conflicts">
                  <div className="spinner"></div>
                  <span>Checking for dietary conflicts...</span>
                </div>
              )}

              {/* Disliked Ingredients */}
              <div className="preference-section">
                <h3>Excluded Ingredients</h3>
                <p>Select ingredients your family wants to avoid:</p>
                <div className="disliked-ingredients">
                  <input
                    type="text"
                    placeholder="Type an ingredient and press Enter"
                    onKeyPress={handleDislikedIngredientKeyPress}
                    className="ingredient-input"
                  />
                  <div className="ingredients-list">
                    {preferences.dislikedIngredients.map((ingredient, index) => (
                      <span key={index} className="ingredient-tag">
                        {ingredient}
                        <button
                          type="button"
                          onClick={() => removeDislikedIngredient(ingredient)}
                          className="remove-ingredient"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* Calorie Target */}
              <div className="preference-section">
                <h3>Family daily calorie goal</h3>
                <p>Optional: Set your family's daily calorie goal:</p>
                <input
                  type="number"
                  placeholder="e.g., 2000"
                  value={preferences.calorieTarget}
                  onChange={(e) => setPreferences(prev => ({
                    ...prev,
                    calorieTarget: e.target.value
                  }))}
                  className="calorie-input"
                  min="1000"
                  max="5000"
                />
              </div>

              {/* Meal Frequency */}
              <div className="preference-section">
                <h3>Meals Per Day</h3>
                <p>Set the daily number of meals for your family:</p>
                <div className="frequency-container">
                  {[2, 3, 4, 5, 6].map(frequency => (
                    <button
                      key={frequency}
                      className={`frequency-button ${preferences.mealFrequency === frequency ? 'frequency-button-selected' : ''}`}
                      onClick={() => setPreferences(prev => ({ ...prev, mealFrequency: frequency }))}
                      type="button"
                    >
                      {frequency}
                    </button>
                  ))}
                </div>
              </div>

              {/* Summary Section */}
              <div className="preference-section summary-section">
                <h3>Summary</h3>
                <div className="summary-card">
                  <div className="summary-row">
                    <span className="summary-label">Restrictions:</span>
                    <span className="summary-value">
                      {preferences.restrictions.length > 0 ? preferences.restrictions.join(', ') : 'None'}
                    </span>
                  </div>
                  <div className="summary-row">
                    <span className="summary-label">Allergies:</span>
                    <span className="summary-value">
                      {preferences.allergies.length > 0 ? preferences.allergies.join(', ') : 'None'}
                    </span>
                  </div>
                  <div className="summary-row">
                    <span className="summary-label">Daily Calories:</span>
                    <span className="summary-value">
                      {preferences.calorieTarget || 'Not set'}
                    </span>
                  </div>
                  <div className="summary-row">
                    <span className="summary-label">Meals per day:</span>
                    <span className="summary-value">
                      {preferences.mealFrequency}
                    </span>
                  </div>
                </div>
              </div>

              {/* Save Button */}
              <div className="save-section">
                <button
                  onClick={savePreferences}
                  disabled={saving}
                  className={`save-preferences-btn ${saving ? 'btn-loading' : ''}`}
                >
                  {saving && <div className="circle-loader small white"></div>}
                  {saving ? 'Saving...' : 'Save Preferences'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>




    </div>
  );
};

export default DietaryPreferences;
