import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import Layout from '../Layout/Layout';
import EditProfileModal from './EditProfileModal';
import ChangePasswordModal from './ChangePasswordModal';
import { FaUser, FaEnvelope, FaMapMarkerAlt, FaCalendarAlt, FaUserClock, FaSignOutAlt, FaEdit, FaKey, FaUserPlus, FaUsers, FaChevronRight, FaPlus, FaTimes, FaLeaf } from 'react-icons/fa';
import { BsGenderAmbiguous } from 'react-icons/bs';
import userAPI from '../../services/userAPI';

function UserProfile() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [familyMembers, setFamilyMembers] = useState([]);
  const [familyLoading, setFamilyLoading] = useState(true);
  const [userPreferences, setUserPreferences] = useState({});
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          navigate('/login');
          return;
        }

        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        const response = await axios.get(`${API_BASE_URL}/users/profile`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        setUser(response.data);
        console.log('User profile data:', response.data);
      } catch (err) {
        setError('Failed to load profile. Please try again.');
        console.error('Profile fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchUserProfile();
  }, [navigate]);

  // Load dietary preferences
  useEffect(() => {
    loadUserPreferences();
  }, []);

  // Fetch family members
  useEffect(() => {
    const fetchFamilyMembers = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) return;

        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        const response = await axios.get(`${API_BASE_URL}/users/family-members`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        setFamilyMembers(response.data.familyMembers || []);
      } catch (err) {
        console.error('Failed to fetch family members:', err);
        setFamilyMembers([]);
      } finally {
        setFamilyLoading(false);
      }
    };

    fetchFamilyMembers();
  }, []);

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';

    const options = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };

    try {
      return new Date(dateString).toLocaleDateString(undefined, options);
    } catch (error) {
      console.error('Date formatting error:', error);
      return dateString;
    }
  };

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return null;

    const today = new Date();
    const birthDate = new Date(dateOfBirth);

    if (isNaN(birthDate.getTime())) return null;

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Load user dietary preferences
  const loadUserPreferences = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token && token !== "undefined") {
        const response = await userAPI.getDietaryPreferences();
        if (response.success && response.dietaryPreferences) {
          setUserPreferences(response.dietaryPreferences);
        } else {
          setUserPreferences({});
        }
      }
    } catch (error) {
      setUserPreferences({});
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    navigate('/login');
  };

  const handleEditProfile = () => {
    setIsEditModalOpen(true);
  };

  const handleChangePassword = () => {
    setIsPasswordModalOpen(true);
  };

  const handleProfileUpdate = (updatedUser) => {
    setUser(updatedUser);
  };

  const handleAddFamilyMember = () => {
    // Navigate to the family page
    console.log('Navigating to family page');
    navigate('/family');
  };

  const handleDietaryPreferences = () => {
    navigate('/dietary-preferences');
  };

  const handleRemoveFamilyMember = async (memberId) => {
    if (!window.confirm('Are you sure you want to remove this family member?')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      await axios.delete(`${API_BASE_URL}/users/family-members/${memberId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // Update local state
      setFamilyMembers(prev => prev.filter(member => member._id !== memberId));
    } catch (err) {
      console.error('Failed to remove family member:', err);
      alert('Failed to remove family member. Please try again.');
    }
  };

  // Content to render inside the layout
  const profileContent = () => {
    if (loading) return <div className="profile-loading"><div className="spinner"></div>Loading profile...</div>;
    if (error) return <div className="profile-error"><p>{error}</p></div>;
    if (!user) return <div className="profile-error"><p>User not found</p></div>;

    return (
      <div className="profile-container">
        <div className="profile-header">
          <div className="profile-title">
            <h1>Profile</h1>
            <p>Manage your personal information and account settings</p>
          </div>
        </div>

        <div className="profile-content">
          <div className="profile-card">
            <div className="profile-avatar">
              {user.profileImage ? (
                <img src={user.profileImage} alt="Profile" />
              ) : (
                <div className="avatar-placeholder">
                  {user.firstName && user.lastName ?
                    `${user.firstName.charAt(0)}${user.lastName.charAt(0)}` :
                    user.username?.charAt(0) || 'U'}
                </div>
              )}
            </div>

            <div className="profile-name1">
              <h2>{user.firstName || ''} {user.lastName || ''}</h2>
              {user.role && <span className="profile-role">{user.role}</span>}
            </div>

            <div className="profile-details">
              <div className="detail-group">
                <h3>Account Information</h3>
                <div className="detail-item">
                  <FaUser className="detail-icon" />
                  <div>
                    <span className="detail-label">Username</span>
                    <span className="detail-value">{user.username || 'N/A'}</span>
                  </div>
                </div>

                <div className="detail-item">
                  <FaEnvelope className="detail-icon" />
                  <div>
                    <span className="detail-label">Email</span>
                    <span className="detail-value">{user.email || 'N/A'}</span>
                  </div>
                </div>
              </div>

              <div className="detail-group">
                <h3>Personal Information</h3>
                {user.barangay && (
                  <div className="detail-item">
                    <FaMapMarkerAlt className="detail-icon" />
                    <div>
                      <span className="detail-label">Barangay</span>
                      <span className="detail-value">{user.barangay}</span>
                    </div>
                  </div>
                )}

                {user.gender && (
                  <div className="detail-item">
                    <BsGenderAmbiguous className="detail-icon" />
                    <div>
                      <span className="detail-label">Gender</span>
                      <span className="detail-value">{user.gender}</span>
                    </div>
                  </div>
                )}

                {user.dateOfBirth && (
                  <div className="detail-item">
                    <FaCalendarAlt className="detail-icon" />
                    <div>
                      <span className="detail-label">Date of Birth</span>
                      <span className="detail-value">
                        {formatDate(user.dateOfBirth).split(',')[0]}
                        {calculateAge(user.dateOfBirth) && (
                          <span className="age-display"> (Age: {calculateAge(user.dateOfBirth)} years)</span>
                        )}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* Dietary Preferences Section */}
              {userPreferences && (userPreferences.restrictions?.length > 0 || userPreferences.allergies?.length > 0) && (
                <div className="detail-group">
                  <h3>Dietary Preferences</h3>
                  <div className="dietary-info-section">
                    <div className="dietary-info-header">
                      <span>🍃 Active Dietary Filters:</span>
                    </div>
                    <div className="dietary-info-content">
                      {userPreferences.restrictions?.length > 0 && (
                        <div className="dietary-info-item">
                          <span className="dietary-label">Restrictions:</span>
                          <span className="dietary-values">{userPreferences.restrictions.join(', ')}</span>
                        </div>
                      )}
                      {userPreferences.allergies?.length > 0 && (
                        <div className="dietary-info-item">
                          <span className="dietary-label">Allergies:</span>
                          <span className="dietary-values">{userPreferences.allergies.join(', ')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              <div className="detail-group">
                <h3>Account Activity</h3>
                <div className="detail-item">
                  <FaUserClock className="detail-icon" />
                  <div>
                    <span className="detail-label">Account Created</span>
                    <span className="detail-value">{formatDate(user.createdAt)}</span>
                  </div>
                </div>

                {user.lastLogin && (
                  <div className="detail-item">
                    <FaUserClock className="detail-icon" />
                    <div>
                      <span className="detail-label">Last Login</span>
                      <span className="detail-value">{formatDate(user.lastLogin)}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Family Members Section */}
          <div className="family-section">
            <div className="family-header">
              <div className="family-title">
                <FaUsers className="family-icon" />
                <div>
                  <h2>Your family group members</h2>
                  <p>View and manage your family group. <a href="/family" className="learn-more">Learn more</a></p>
                </div>
              </div>
            </div>

            <div className="family-members">
              {/* Current User */}
              <div className="family-member current-user">
                <div className="member-avatar">
                  {user.profileImage ? (
                    <img src={user.profileImage} alt={`${user.firstName} ${user.lastName}`} />
                  ) : (
                    <div className="avatar-placeholder">
                      {user.firstName && user.lastName ?
                        `${user.firstName.charAt(0)}${user.lastName.charAt(0)}` :
                        user.username?.charAt(0) || 'U'}
                    </div>
                  )}
                </div>
                <div className="member-info">
                  <div className="member-name">{user.firstName} {user.lastName}</div>
                  <div className="member-role">Family manager</div>
                  {user.dateOfBirth && calculateAge(user.dateOfBirth) && (
                    <div className="member-age">Age: {calculateAge(user.dateOfBirth)} years</div>
                  )}
                </div>
                <FaChevronRight className="member-arrow" />
              </div>

              {/* Family Members */}
              {familyLoading ? (
                <div className="family-loading">Loading family members...</div>
              ) : familyMembers.length > 0 ? (
                familyMembers.map((member, index) => (
                  <div key={member._id || index} className="family-member">
                    <div className="member-avatar">
                      <div className="avatar-placeholder">
                        {member.name ? member.name.charAt(0).toUpperCase() : 'M'}
                      </div>
                    </div>
                    <div className="member-info">
                      <div className="member-name">{member.name}</div>
                      <div className="member-role">Member</div>
                      {member.dateOfBirth && calculateAge(member.dateOfBirth) && (
                        <div className="member-age">Age: {calculateAge(member.dateOfBirth)} years</div>
                      )}
                    </div>
                    <div className="member-actions">
                      <button
                        className="remove-member-btn"
                        onClick={() => handleRemoveFamilyMember(member._id)}
                        title="Remove family member"
                      >
                        <FaTimes />
                      </button>
                      <FaChevronRight className="member-arrow" />
                    </div>
                  </div>
                ))
              ) : (
                <div className="no-family-members">
                  <p>No family members added yet.</p>
                </div>
              )}

              {/* Add Family Member Button */}
              <div className="add-member-section">
                <button className="add-member-btn" onClick={handleAddFamilyMember}>
                  <FaPlus className="add-icon" />
                  <span>Send invitations</span>
                </button>
              </div>
            </div>

            {/* Family Sharing Section */}
            <div className="family-sharing">
              <h3>Family sharing</h3>
              <p>Get more from PanlasApp by sharing meal plans and preferences with your family. <a href="/family" className="learn-more">Learn more</a></p>
            </div>
          </div>

          <div className="profile-actions">
            <button className="action-btn edit-profile-btn" onClick={handleEditProfile}>
              <FaEdit /> Edit Profile
            </button>
            <button className="action-btn change-password-btn" onClick={handleChangePassword}>
              <FaKey /> Change Password
            </button>
            <button className="action-btn dietary-preferences-btn" onClick={handleDietaryPreferences}>
              <FaLeaf /> Dietary Preferences
            </button>
            <button className="action-btn add-family-btn" onClick={handleAddFamilyMember}>
              <FaUserPlus /> Add Family Member
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Layout>
      {profileContent()}

      {/* Modals */}
      <EditProfileModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        user={user}
        onProfileUpdate={handleProfileUpdate}
      />

      <ChangePasswordModal
        isOpen={isPasswordModalOpen}
        onClose={() => setIsPasswordModalOpen(false)}
      />
    </Layout>
  );
}

export default UserProfile;
