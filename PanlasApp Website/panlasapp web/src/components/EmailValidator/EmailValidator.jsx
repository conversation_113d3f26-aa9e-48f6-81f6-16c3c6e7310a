import React from 'react';

const EmailValidator = ({ email, showValidation = false }) => {
  const validateEmail = (email) => {
    if (!email) return { isValid: true, errors: [] };

    const errors = [];

    // Gmail-specific regex check (comprehensive validation)
    const gmailRegex = /^[^\s@]+@gmail\.com$/i;

    if (!email.includes('@')) {
      errors.push('Email must contain @ symbol');
    } else if (!gmailRegex.test(email)) {
      // Check specific issues
      const parts = email.split('@');
      if (parts.length !== 2) {
        errors.push('Email must have exactly one @ symbol');
      } else {
        const [localPart, domainPart] = parts;

        // Check local part (before @)
        if (localPart.length === 0) {
          errors.push('Email must have text before @ symbol');
        }

        // Check domain part (after @) - Must be Gmail
        if (domainPart.length === 0) {
          errors.push('Email must have domain after @ symbol');
        } else if (domainPart.toLowerCase() !== 'gmail.com') {
          errors.push('Email must be gmail');
        }
      }

      // Check for spaces
      if (email.includes(' ')) {
        errors.push('Email cannot contain spaces');
      }

      // Check for consecutive dots
      if (email.includes('..')) {
        errors.push('Email cannot have consecutive dots');
      }

      // Check if starts or ends with dot
      if (email.startsWith('.') || email.endsWith('.')) {
        errors.push('Email cannot start or end with a dot');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  const validation = validateEmail(email);
  
  if (!showValidation || !email) return null;

  return (
    <div className="email-validator">
      {validation.isValid ? (
        <div className="email-validator valid">
          <span>✓</span>
          <span>Valid email format</span>
        </div>
      ) : (
        <div className="email-validator invalid">
          {validation.errors.map((error, index) => (
            <div key={index} className="error-item">
              <span>⚠</span>
              <span>{error}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default EmailValidator;
