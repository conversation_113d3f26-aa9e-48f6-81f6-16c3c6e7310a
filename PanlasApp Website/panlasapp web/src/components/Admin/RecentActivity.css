.recent-activity {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.recent-activity h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
}

.activity-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.activity-item {
  display: flex;
  padding: 1rem 0;
  border-bottom: 1px solid #eee;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #f5f5f5;
  border-radius: 50%;
}

.activity-content {
  flex: 1;
}

.activity-user {
  font-weight: bold;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-badge {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #8b4513;
  font-size: 0.7rem;
  font-weight: 700;
  padding: 0.15rem 0.4rem;
  border-radius: 12px;
  border: 1px solid #daa520;
  text-shadow: 0 1px 1px rgba(255,255,255,0.5);
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.activity-email {
  font-size: 0.85rem;
  color: #888;
  font-weight: normal;
}

.activity-debug {
  font-size: 0.7rem;
  color: #666;
  margin-top: 4px;
  font-style: italic;
}

.activity-description {
  color: #555;
  margin-bottom: 0.25rem;
}

.activity-time {
  font-size: 0.8rem;
  color: #888;
}

.no-activity {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

.loading-activity, .error-activity {
  text-align: center;
  padding: 2rem;
}

.error-activity {
  color: #f44336;
}
