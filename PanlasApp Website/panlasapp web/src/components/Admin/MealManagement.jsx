import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Swal from 'sweetalert2';
import { FaPlus, FaEdit, FaTrash, FaSearch, FaSave, FaTimes, FaEye } from 'react-icons/fa';
import Layout from '../Layout/Layout';
import './MealManagement.css';

// Helper to convert tag to camelCase key for dietType
const tagToDietTypeKey = (tag) => {
  // Remove non-alphanumeric, split, capitalize, join, and prefix with 'is'
  const camel = tag
    .replace(/[^a-zA-Z0-9 ]/g, ' ')
    .split(' ')
    .map((w, i) => i === 0 ? w.toLowerCase() : w.charAt(0).toUpperCase() + w.slice(1).toLowerCase())
    .join('');
  return `is${camel.charAt(0).toUpperCase()}${camel.slice(1)}`;
};

const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack', 'appetizer', 'Other'];
const categories = ['Meat', 'Seafood', 'Vegetable', 'Soup', 'Stew', 'Rice', 'Noodles', 'Dessert', 'Fish', 'Salad', 'Tofu'];
const commonDietaryTags = [
  'Vegetarian',
  'Flexitarian',
  'Vegan',
  'Dairy-Free',
  'Egg-Free',
  'Gluten-Free',
  'Soy-Free',
  'Nut-Free',
  'Low-Carb',
  'Low-Sugar',
  'Sugar-Free',
  'Low-Fat',
  'Low-Sodium',
  'Organic',
  'Halal',
  'High-Protein',
  'Pescatarian',
  'Keto',
  'Plant-Based',
  'Kosher',
  'Climatarian',
  'Raw Food',
  'Mediterranean',
  'Paleo',
  'Kangatarian',
  'Pollotarian',
  'Other'
  
];
const commonAllergens = [
  'Gluten',
  'Dairy',
  'Nuts',
  'Soy',
  'Eggs',
  'Shellfish',
  'Fish',
  'Milk',
  'Peanuts',
  'Tree Nuts',
  'Wheat',
  'Soybeans',
  'Sesame',
  'Mustard',
  'Celery',
  'Lupin',
  'Mollusks',
  'Sulfites',
  'Other'
];
const regions = ['Philippines', 'Luzon', 'Visayas', 'Mindanao', 'Metro Manila', 'Bicol', 'Ilocos'];

// Generate initial dietType object from tags
const initialDietType = {};
commonDietaryTags.forEach(tag => {
  initialDietType[tagToDietTypeKey(tag)] = false;
});

const MealManagement = () => {
  const [meals, setMeals] = useState([]);
  const [filteredMeals, setFilteredMeals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('view');
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [mealTypeFilter, setMealTypeFilter] = useState('all');
  const [showMealForm, setShowMealForm] = useState(false);
  const [editingMeal, setEditingMeal] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [mealsPerPage] = useState(10);
  const [showMealDetails, setShowMealDetails] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState(null);

  // Custom input states for "Other" options
  const [showCustomDietaryInput, setShowCustomDietaryInput] = useState(false);
  const [showCustomMealTypeInput, setShowCustomMealTypeInput] = useState(false);
  const [showCustomAllergenInput, setShowCustomAllergenInput] = useState(false);
  const [showCustomDietTypeInput, setShowCustomDietTypeInput] = useState(false);
  const [customDietaryInput, setCustomDietaryInput] = useState('');
  const [customMealTypeInput, setCustomMealTypeInput] = useState('');
  const [customAllergenInput, setCustomAllergenInput] = useState('');
  const [customDietTypeInput, setCustomDietTypeInput] = useState('');

  // Form state for meal creation/editing
  const [mealForm, setMealForm] = useState({
    name: '',
    mealType: [],
    category: '',
    dietaryTags: [],
    rating: '',
    prepTime: '',
    calories: '',
    protein: '',
    carbs: '',
    fat: '',
    calcium: '',
    phosphorus: '',
    iron: '',
    vitaminA: '',
    vitaminC: '',
    vitaminB3: '',
    vitaminB1: '',
    vitaminB2: '',
    price: '',
    image: '',
    description: '',
    ingredients: [],
    instructions: [],
    allergens: [],
    region: 'Philippines',
    servingSize: 4,
    dietType: { ...initialDietType }
  });

  // Fetch meals from API
  const fetchMeals = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.get(`${API_BASE_URL}/meals`, {
        headers: { 'x-auth-token': token }
      });
      setMeals(response.data);
      setFilteredMeals(response.data);
      setError('');
    } catch (err) {
      setError('Failed to fetch meals. Please try again.');
      console.error('Error fetching meals:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMeals();
  }, []);

  // Filter meals based on search and filters
  useEffect(() => {
    let filtered = meals;

    // Search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(meal =>
        meal.name.toLowerCase().includes(searchLower) ||
        meal.description.toLowerCase().includes(searchLower) ||
        (Array.isArray(meal.category) ? meal.category.join(' ') : meal.category).toLowerCase().includes(searchLower)
      );
    }

    // Category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(meal => {
        if (Array.isArray(meal.category)) {
          return meal.category.includes(categoryFilter);
        }
        return meal.category === categoryFilter;
      });
    }

    // Meal type filter
    if (mealTypeFilter !== 'all') {
      filtered = filtered.filter(meal => 
        Array.isArray(meal.mealType) 
          ? meal.mealType.includes(mealTypeFilter)
          : meal.mealType === mealTypeFilter
      );
    }

    setFilteredMeals(filtered);
    setCurrentPage(1);
  }, [meals, searchTerm, categoryFilter, mealTypeFilter]);

  // Reset form
  const resetForm = () => {
    setMealForm({
      name: '',
      mealType: [],
      category: '',
      dietaryTags: [],
      rating: '',
      prepTime: '',
      calories: '',
      protein: '',
      carbs: '',
      fat: '',
      calcium: '',
      phosphorus: '',
      iron: '',
      vitaminA: '',
      vitaminC: '',
      vitaminB3: '',
      vitaminB1: '',
      vitaminB2: '',
      price: '',
      image: '',
      description: '',
      ingredients: [],
      instructions: [],
      allergens: [],
      region: 'Philippines',
      servingSize: 4,
      dietType: { ...initialDietType }
    });
    setEditingMeal(null);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    if (name.startsWith('dietType.')) {
      const dietTypeField = name.split('.')[1];
      setMealForm(prev => ({
        ...prev,
        dietType: {
          ...prev.dietType,
          [dietTypeField]: checked
        }
      }));
    } else if (type === 'checkbox') {
      setMealForm(prev => ({
        ...prev,
        [name]: checked
      }));
    } else if (type === 'number') {
      setMealForm(prev => ({
        ...prev,
        [name]: value === '' ? '' : parseFloat(value) || 0
      }));
    } else {
      setMealForm(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle array inputs (ingredients, instructions, etc.)
  const handleArrayInput = (field, value) => {
    const items = value.split('\n').filter(item => item.trim() !== '');
    setMealForm(prev => ({
      ...prev,
      [field]: items
    }));
  };

  // Handle multi-select inputs
  const handleMultiSelect = (field, value) => {
    if (value === 'Other') {
      // Handle "Other" option specially
      if (field === 'dietaryTags') {
        setShowCustomDietaryInput(!showCustomDietaryInput);
      } else if (field === 'mealType') {
        setShowCustomMealTypeInput(!showCustomMealTypeInput);
      } else if (field === 'allergens') {
        setShowCustomAllergenInput(!showCustomAllergenInput);
      }
      return;
    }

    setMealForm(prev => {
      const currentValues = prev[field] || [];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(item => item !== value)
        : [...currentValues, value];
      return {
        ...prev,
        [field]: newValues
      };
    });
  };

  // Add custom dietary tag
  const addCustomDietaryTag = () => {
    if (customDietaryInput.trim()) {
      const customTags = customDietaryInput.split(',').map(tag => tag.trim()).filter(tag => tag);
      setMealForm(prev => ({
        ...prev,
        dietaryTags: [...new Set([...prev.dietaryTags, ...customTags])]
      }));
      setCustomDietaryInput('');
      setShowCustomDietaryInput(false);
    }
  };

  // Add custom meal type
  const addCustomMealType = () => {
    if (customMealTypeInput.trim()) {
      const customTypes = customMealTypeInput.split(',').map(type => type.trim()).filter(type => type);
      setMealForm(prev => ({
        ...prev,
        mealType: [...new Set([...prev.mealType, ...customTypes])]
      }));
      setCustomMealTypeInput('');
      setShowCustomMealTypeInput(false);
    }
  };

  // Add custom allergen
  const addCustomAllergen = () => {
    if (customAllergenInput.trim()) {
      const customAllergens = customAllergenInput.split(',').map(allergen => allergen.trim()).filter(allergen => allergen);
      setMealForm(prev => ({
        ...prev,
        allergens: [...new Set([...prev.allergens, ...customAllergens])]
      }));
      setCustomAllergenInput('');
      setShowCustomAllergenInput(false);
    }
  };

  // Add custom diet type
  const addCustomDietType = () => {
    if (customDietTypeInput.trim()) {
      const customDietTypes = customDietTypeInput.split(',').map(type => type.trim()).filter(type => type);
      setMealForm(prev => {
        const newDietType = { ...prev.dietType };
        customDietTypes.forEach(type => {
          const key = tagToDietTypeKey(type);
          newDietType[key] = true;
        });
        return {
          ...prev,
          dietType: newDietType
        };
      });
      setCustomDietTypeInput('');
      setShowCustomDietTypeInput(false);
    }
  };

  // Handle diet type selection (special case for "Other")
  const handleDietTypeSelect = (tag) => {
    if (tag === 'Other') {
      setShowCustomDietTypeInput(!showCustomDietTypeInput);
      return;
    }

    // Normal diet type handling
    const key = tagToDietTypeKey(tag);
    const event = {
      target: {
        name: `dietType.${key}`,
        checked: !mealForm.dietType[key]
      }
    };
    handleInputChange(event);
  };

  // Save meal (create or update)
  const saveMeal = async () => {
    try {
      const token = localStorage.getItem('token');
      const config = {
        headers: { 
          'x-auth-token': token,
          'Content-Type': 'application/json'
        }
      };

      // Prepare meal data - convert empty strings to 0 for numeric fields
      const mealData = {
        ...mealForm,
        category: Array.isArray(mealForm.category) ? mealForm.category : [mealForm.category],
        // Convert empty strings to 0 for numeric fields
        rating: mealForm.rating === '' ? 0 : parseFloat(mealForm.rating) || 0,
        prepTime: mealForm.prepTime === '' ? 0 : parseFloat(mealForm.prepTime) || 0,
        calories: mealForm.calories === '' ? 0 : parseFloat(mealForm.calories) || 0,
        protein: mealForm.protein === '' ? 0 : parseFloat(mealForm.protein) || 0,
        carbs: mealForm.carbs === '' ? 0 : parseFloat(mealForm.carbs) || 0,
        fat: mealForm.fat === '' ? 0 : parseFloat(mealForm.fat) || 0,
        calcium: mealForm.calcium === '' ? 0 : parseFloat(mealForm.calcium) || 0,
        phosphorus: mealForm.phosphorus === '' ? 0 : parseFloat(mealForm.phosphorus) || 0,
        iron: mealForm.iron === '' ? 0 : parseFloat(mealForm.iron) || 0,
        vitaminA: mealForm.vitaminA === '' ? 0 : parseFloat(mealForm.vitaminA) || 0,
        vitaminC: mealForm.vitaminC === '' ? 0 : parseFloat(mealForm.vitaminC) || 0,
        vitaminB3: mealForm.vitaminB3 === '' ? 0 : parseFloat(mealForm.vitaminB3) || 0,
        vitaminB1: mealForm.vitaminB1 === '' ? 0 : parseFloat(mealForm.vitaminB1) || 0,
        vitaminB2: mealForm.vitaminB2 === '' ? 0 : parseFloat(mealForm.vitaminB2) || 0,
        price: mealForm.price === '' ? 0 : parseFloat(mealForm.price) || 0
      };

      if (editingMeal) {
        // Update existing meal
        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        await axios.put(`${API_BASE_URL}/meals/${editingMeal._id}`, mealData, config);
        alert('Meal updated successfully!');
      } else {
        // Create new meal
        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        await axios.post(`${API_BASE_URL}/meals`, mealData, config);
        alert('Meal created successfully!');
      }

      resetForm();
      setShowMealForm(false);
      setActiveTab('view');
      fetchMeals();
    } catch (err) {
      alert('Error saving meal: ' + (err.response?.data?.message || err.message));
      console.error('Error saving meal:', err);
    }
  };

  // Edit meal
  const editMeal = (meal) => {
    // Ensure all possible dietType keys exist
    const dietType = { ...initialDietType, ...(meal.dietType || {}) };
    setMealForm({
      ...meal,
      mealType: Array.isArray(meal.mealType) ? meal.mealType : [meal.mealType],
      category: Array.isArray(meal.category) ? meal.category[0] : meal.category,
      dietaryTags: meal.dietaryTags || [],
      ingredients: meal.ingredients || [],
      instructions: meal.instructions || [],
      allergens: meal.allergens || [],
      calcium: meal.calcium || 0,
      phosphorus: meal.phosphorus || 0,
      iron: meal.iron || 0,
      vitaminA: meal.vitaminA || 0,
      vitaminC: meal.vitaminC || 0,
      vitaminB3: meal.vitaminB3 || 0,
      vitaminB1: meal.vitaminB1 || 0,
      vitaminB2: meal.vitaminB2 || 0,
      region: meal.region || 'Philippines',
      servingSize: meal.servingSize || 4,
      dietType
    });
    setEditingMeal(meal);
    setShowMealForm(true);
    setActiveTab('add');
  };

  // Delete meal
  const deleteMeal = async (mealId, mealName) => {
    if (window.confirm(`Are you sure you want to delete "${mealName}"? This action cannot be undone.`)) {
      try {
        const token = localStorage.getItem('token');
        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        await axios.delete(`${API_BASE_URL}/meals/${mealId}`, {
          headers: { 'x-auth-token': token }
        });
        alert('Meal deleted successfully!');
        fetchMeals();
      } catch (err) {
        alert('Error deleting meal: ' + (err.response?.data?.message || err.message));
        console.error('Error deleting meal:', err);
      }
    }
  };

  // View meal details
  const viewMealDetails = (meal) => {
    setSelectedMeal(meal);
    setShowMealDetails(true);
  };

  // Pagination
  const indexOfLastMeal = currentPage * mealsPerPage;
  const indexOfFirstMeal = indexOfLastMeal - mealsPerPage;
  const currentMeals = filteredMeals.slice(indexOfFirstMeal, indexOfLastMeal);
  const totalPages = Math.ceil(filteredMeals.length / mealsPerPage);

  if (loading) {
    return (
      <Layout>
        <div className="loading">Loading meal management...</div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="meal-management">
        <h1>Meal Management</h1>

        <div className="meal-tabs">
          <button
            className={`tab-button ${activeTab === 'view' ? 'active' : ''}`}
            onClick={() => {
              setActiveTab('view');
              setShowMealForm(false);
              setShowMealDetails(false);
              resetForm();
            }}
          >
            <FaSearch /> View All Meals ({meals.length})
          </button>
          <button
            className={`tab-button ${activeTab === 'add' ? 'active' : ''}`}
            onClick={() => {
              setActiveTab('add');
              setShowMealForm(true);
              setShowMealDetails(false);
              if (!editingMeal) resetForm();
            }}
          >
            <FaPlus /> {editingMeal ? 'Edit Meal' : 'Add New Meal'}
          </button>
        </div>

        {error && <div className="error-message">{error}</div>}

        {activeTab === 'view' && !showMealDetails && (
          <div className="meals-view">
            {/* Search and Filter Controls */}
            <div className="meal-filters">
              <div className="search-box">
                <FaSearch className="search-icon" />
                <input
                  type="text"
                  placeholder="Search meals by name, description, or category..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
              </div>
              <div className="filter-controls">
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="filter-select"
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
                <select
                  value={mealTypeFilter}
                  onChange={(e) => setMealTypeFilter(e.target.value)}
                  className="filter-select"
                >
                  <option value="all">All Meal Types</option>
                  {mealTypes.map(type => (
                    <option key={type} value={type}>{type.charAt(0).toUpperCase() + type.slice(1)}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Meals Statistics */}
            <div className="meals-stats">
              <div className="stat-card">
                <h3>Total Meals</h3>
                <p>{meals.length}</p>
              </div>
              <div className="stat-card">
                <h3>Filtered Results</h3>
                <p>{filteredMeals.length}</p>
              </div>
              <div className="stat-card">
                <h3>Categories</h3>
                <p>{new Set(meals.flatMap(meal => Array.isArray(meal.category) ? meal.category : [meal.category])).size}</p>
              </div>
              <div className="stat-card">
                <h3>Average Rating</h3>
                <p>{meals.length > 0 ? (meals.reduce((sum, meal) => sum + (meal.rating || 0), 0) / meals.length).toFixed(1) : '0'}</p>
              </div>
            </div>

            {/* Meals Table */}
            <div className="meals-table-container">
              <table className="meals-table">
                <thead>
                  <tr>
                    <th>Image</th>
                    <th>Name</th>
                    <th>Category</th>
                    <th>Meal Type</th>
                    <th>Price (₱)</th>
                    <th>Calories</th>
                    <th>Rating</th>
                    <th>Region</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {currentMeals.length > 0 ? (
                    currentMeals.map((meal) => (
                      <tr key={meal._id}>
                        <td>
                          <div className="meal-image">
                            {meal.image ? (
                              <img src={meal.image} alt={meal.name} />
                            ) : (
                              <div className="no-image">No Image</div>
                            )}
                          </div>
                        </td>
                        <td>
                          <div className="meal-name">
                            <strong>{meal.name}</strong>
                            <small>{meal.description?.substring(0, 50)}...</small>
                          </div>
                        </td>
                        <td>
                          {Array.isArray(meal.category)
                            ? meal.category.join(', ')
                            : meal.category}
                        </td>
                        <td>
                          {Array.isArray(meal.mealType)
                            ? meal.mealType.join(', ')
                            : meal.mealType}
                        </td>
                        <td>₱{meal.price}</td>
                        <td>{meal.calories}</td>
                        <td>
                          <div className="rating">
                            {'★'.repeat(Math.floor(meal.rating || 0))}
                            <span>{meal.rating || 0}</span>
                          </div>
                        </td>
                        <td>{meal.region || 'Philippines'}</td>
                        <td>
                          <div className="action-buttons">
                            <button
                              className="btn-view"
                              onClick={() => viewMealDetails(meal)}
                              title="View Details"
                            >
                              <FaEye />
                            </button>
                            <button
                              className="btn-edit"
                              onClick={() => editMeal(meal)}
                              title="Edit Meal"
                            >
                              <FaEdit />
                            </button>
                            <button
                              className="btn-delete"
                              onClick={() => deleteMeal(meal._id, meal.name)}
                              title="Delete Meal"
                            >
                              <FaTrash />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="9" className="no-meals">
                        No meals found matching the current filters.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="pagination">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="pagination-btn"
                >
                  Previous
                </button>
                <span className="pagination-info">
                  Page {currentPage} of {totalPages} ({filteredMeals.length} meals)
                </span>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="pagination-btn"
                >
                  Next
                </button>
              </div>
            )}
          </div>
        )}

        {/* Meal Details View */}
        {showMealDetails && selectedMeal && (
          <div className="meal-details-view">
            <div className="details-header">
              <h2>Meal Details: {selectedMeal.name}</h2>
              <button
                className="btn-close"
                onClick={() => {
                  setShowMealDetails(false);
                  setSelectedMeal(null);
                }}
              >
                <FaTimes />
              </button>
            </div>

            <div className="details-content">
              <div className="details-grid">
                <div className="detail-section">
                  <h3>Basic Information</h3>
                  <div className="detail-item">
                    <strong>Name:</strong> {selectedMeal.name}
                  </div>
                  <div className="detail-item">
                    <strong>Description:</strong> {selectedMeal.description}
                  </div>
                  <div className="detail-item">
                    <strong>Category:</strong> {Array.isArray(selectedMeal.category) ? selectedMeal.category.join(', ') : selectedMeal.category}
                  </div>
                  <div className="detail-item">
                    <strong>Meal Type:</strong> {Array.isArray(selectedMeal.mealType) ? selectedMeal.mealType.join(', ') : selectedMeal.mealType}
                  </div>
                  <div className="detail-item">
                    <strong>Region:</strong> {selectedMeal.region || 'Philippines'}
                  </div>
                  <div className="detail-item">
                    <strong>Serving Size:</strong> {selectedMeal.servingSize || 4} people
                  </div>
                  <div className="detail-item">
                    <strong>Prep Time:</strong> {selectedMeal.prepTime} minutes
                  </div>
                  <div className="detail-item">
                    <strong>Price:</strong> ₱{selectedMeal.price}
                  </div>
                  <div className="detail-item">
                    <strong>Rating:</strong> {selectedMeal.rating || 0}/5 ★
                  </div>
                </div>

                <div className="detail-section">
                  <h3>Nutritional Information</h3>
                  <div className="nutrition-grid">
                    <div className="nutrition-item">
                      <strong>Calories:</strong> {selectedMeal.calories}
                    </div>
                    <div className="nutrition-item">
                      <strong>Protein:</strong> {selectedMeal.protein}g
                    </div>
                    <div className="nutrition-item">
                      <strong>Carbs:</strong> {selectedMeal.carbs}g
                    </div>
                    <div className="nutrition-item">
                      <strong>Fat:</strong> {selectedMeal.fat}g
                    </div>
                    {selectedMeal.calcium && (
                      <div className="nutrition-item">
                        <strong>Calcium:</strong> {selectedMeal.calcium}mg
                      </div>
                    )}
                    {selectedMeal.iron && (
                      <div className="nutrition-item">
                        <strong>Iron:</strong> {selectedMeal.iron}mg
                      </div>
                    )}
                    {selectedMeal.vitaminA && (
                      <div className="nutrition-item">
                        <strong>Vitamin A:</strong> {selectedMeal.vitaminA}μg
                      </div>
                    )}
                    {selectedMeal.vitaminC && (
                      <div className="nutrition-item">
                        <strong>Vitamin C:</strong> {selectedMeal.vitaminC}mg
                      </div>
                    )}
                  </div>
                </div>

                <div className="detail-section">
                  <h3>Dietary Information</h3>
                  <div className="dietary-tags">
                    {selectedMeal.dietaryTags && selectedMeal.dietaryTags.length > 0 && (
                      <div className="detail-item">
                        <strong>Dietary Tags:</strong>
                        <div className="tags">
                          {selectedMeal.dietaryTags.map(tag => (
                            <span key={tag} className="tag">{tag}</span>
                          ))}
                        </div>
                      </div>
                    )}
                    {selectedMeal.allergens && selectedMeal.allergens.length > 0 && (
                      <div className="detail-item">
                        <strong>Allergens:</strong>
                        <div className="tags">
                          {selectedMeal.allergens.map(allergen => (
                            <span key={allergen} className="tag allergen-tag">{allergen}</span>
                          ))}
                        </div>
                      </div>
                    )}
                    <div className="diet-types">
                      {selectedMeal.dietType && Object.entries(selectedMeal.dietType).map(([key, value]) => (
                        value && (
                          <span key={key} className="diet-type">
                            {key.replace(/^is/, '').replace(/([A-Z])/g, ' $1').trim()}
                          </span>
                        )
                      ))}
                    </div>
                  </div>
                </div>

                <div className="detail-section full-width">
                  <h3>Ingredients</h3>
                  <ul className="ingredients-list">
                    {selectedMeal.ingredients && selectedMeal.ingredients.map((ingredient, index) => (
                      <li key={index}>{ingredient}</li>
                    ))}
                  </ul>
                </div>

                <div className="detail-section full-width">
                  <h3>Instructions</h3>
                  <ol className="instructions-list">
                    {selectedMeal.instructions && selectedMeal.instructions.map((instruction, index) => (
                      <li key={index}>{instruction}</li>
                    ))}
                  </ol>
                </div>

                {selectedMeal.image && (
                  <div className="detail-section full-width">
                    <h3>Image</h3>
                    <div className="meal-image-large">
                      <img src={selectedMeal.image} alt={selectedMeal.name} />
                    </div>
                  </div>
                )}
              </div>

              <div className="details-actions">
                <button
                  className="btn-edit"
                  onClick={() => {
                    editMeal(selectedMeal);
                    setShowMealDetails(false);
                  }}
                >
                  <FaEdit /> Edit Meal
                </button>
                <button
                  className="btn-delete"
                  onClick={() => {
                    deleteMeal(selectedMeal._id, selectedMeal.name);
                    setShowMealDetails(false);
                  }}
                >
                  <FaTrash /> Delete Meal
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Meal Form */}
        {activeTab === 'add' && showMealForm && (
          <div className="meal-form-container">
            <div className="meal-form">
              <div className="form-header">
                <h2>{editingMeal ? 'Edit Meal' : 'Add New Meal'}</h2>
                <button
                  className="btn-close"
                  onClick={() => {
                    setShowMealForm(false);
                    resetForm();
                    setActiveTab('view');
                  }}
                >
                  <FaTimes />
                </button>
              </div>

              <div className="form-grid">
                {/* Basic Information */}
                <div className="form-section">
                  <h3>Basic Information</h3>
                  <div className="form-group">
                    <label>Meal Name *</label>
                    <input
                      type="text"
                      name="name"
                      value={mealForm.name}
                      onChange={handleInputChange}
                      placeholder="Enter meal name"
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label>Category *</label>
                    <select
                      name="category"
                      value={mealForm.category}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="">Select Category</option>
                      {categories.map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Meal Types *</label>
                    <div className="checkbox-group">
                      {mealTypes.map(type => (
                        <label
                          key={type}
                          className={`checkbox-label ${
                            type === 'Other'
                              ? (showCustomMealTypeInput ? 'checked' : '')
                              : (mealForm.mealType.includes(type) ? 'checked' : '')
                          }`}
                        >
                          <input
                            type="checkbox"
                            checked={
                              type === 'Other'
                                ? showCustomMealTypeInput
                                : mealForm.mealType.includes(type)
                            }
                            onChange={() => handleMultiSelect('mealType', type)}
                          />
                          <span>{type.charAt(0).toUpperCase() + type.slice(1)}</span>
                        </label>
                      ))}
                    </div>

                    {/* Custom Meal Type Input */}
                    {showCustomMealTypeInput && (
                      <div className="custom-input-container" style={{backgroundColor: '#f0f0f0', padding: '10px', margin: '10px 0', border: '2px solid #20C5AF'}}>
                        <input
                          type="text"
                          value={customMealTypeInput}
                          onChange={(e) => setCustomMealTypeInput(e.target.value)}
                          placeholder="Enter custom meal type (separate multiple with commas)"
                          className="custom-input"
                          onKeyDown={(e) => e.key === 'Enter' && addCustomMealType()}
                          style={{flex: 1, padding: '8px', marginRight: '8px'}}
                        />
                        <button
                          type="button"
                          onClick={addCustomMealType}
                          className="add-custom-btn"
                          style={{padding: '8px 16px', backgroundColor: '#20C5AF', color: 'white', border: 'none', borderRadius: '4px'}}
                        >
                          Add
                        </button>
                      </div>
                    )}


                  </div>

                  <div className="form-group">
                    <label>Description</label>
                    <textarea
                      name="description"
                      value={mealForm.description}
                      onChange={handleInputChange}
                      placeholder="Enter meal description"
                      rows="3"
                    />
                  </div>

                  <div className="form-group">
                    <label>Image URL</label>
                    <input
                      type="url"
                      name="image"
                      value={mealForm.image}
                      onChange={handleInputChange}
                      placeholder="Enter image URL"
                    />
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label>Region</label>
                      <select
                        name="region"
                        value={mealForm.region}
                        onChange={handleInputChange}
                      >
                        {regions.map(region => (
                          <option key={region} value={region}>{region}</option>
                        ))}
                      </select>
                    </div>
                    <div className="form-group">
                      <label>Serving Size</label>
                      <input
                        type="number"
                        name="servingSize"
                        value={mealForm.servingSize}
                        onChange={handleInputChange}
                        min="1"
                        max="20"
                      />
                    </div>
                  </div>
                </div>

                {/* Nutritional Information */}
                <div className="form-section">
                  <h3>Nutritional Information</h3>
                  <div className="form-row">
                    <div className="form-group">
                      <label>Calories</label>
                      <input
                        type="number"
                        name="calories"
                        value={mealForm.calories}
                        onChange={handleInputChange}
                        min="0"
                      />
                    </div>
                    <div className="form-group">
                      <label>Protein (g)</label>
                      <input
                        type="number"
                        name="protein"
                        value={mealForm.protein}
                        onChange={handleInputChange}
                        min="0"
                        step="0.1"
                      />
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label>Carbs (g)</label>
                      <input
                        type="number"
                        name="carbs"
                        value={mealForm.carbs}
                        onChange={handleInputChange}
                        min="0"
                        step="0.1"
                      />
                    </div>
                    <div className="form-group">
                      <label>Fat (g)</label>
                      <input
                        type="number"
                        name="fat"
                        value={mealForm.fat}
                        onChange={handleInputChange}
                        min="0"
                        step="0.1"
                      />
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label>Calcium (mg)</label>
                      <input
                        type="number"
                        name="calcium"
                        value={mealForm.calcium}
                        onChange={handleInputChange}
                        min="0"
                        step="0.1"
                      />
                    </div>
                    <div className="form-group">
                      <label>Iron (mg)</label>
                      <input
                        type="number"
                        name="iron"
                        value={mealForm.iron}
                        onChange={handleInputChange}
                        min="0"
                        step="0.1"
                      />
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label>Vitamin A (μg)</label>
                      <input
                        type="number"
                        name="vitaminA"
                        value={mealForm.vitaminA}
                        onChange={handleInputChange}
                        min="0"
                        step="0.1"
                      />
                    </div>
                    <div className="form-group">
                      <label>Vitamin C (mg)</label>
                      <input
                        type="number"
                        name="vitaminC"
                        value={mealForm.vitaminC}
                        onChange={handleInputChange}
                        min="0"
                        step="0.1"
                      />
                    </div>
                  </div>
                </div>

                {/* Additional Details */}
                <div className="form-section">
                  <h3>Additional Details</h3>
                  <div className="form-row">
                    <div className="form-group">
                      <label>Price (₱)</label>
                      <input
                        type="number"
                        name="price"
                        value={mealForm.price}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                      />
                    </div>
                    <div className="form-group">
                      <label>Prep Time (minutes)</label>
                      <input
                        type="number"
                        name="prepTime"
                        value={mealForm.prepTime}
                        onChange={handleInputChange}
                        min="0"
                      />
                    </div>
                  </div>

                  <div className="form-group">
                    <label>Rating (0-5)</label>
                    <input
                      type="number"
                      name="rating"
                      value={mealForm.rating}
                      onChange={handleInputChange}
                      min="0"
                      max="5"
                      step="0.1"
                    />
                  </div>
                </div>

                {/* Vitamins & Minerals */}
                <div className="form-section">
                  <h3>Additional Vitamins & Minerals</h3>
                  <div className="form-row">
                    <div className="form-group">
                      <label>Phosphorus (mg)</label>
                      <input
                        type="number"
                        name="phosphorus"
                        value={mealForm.phosphorus}
                        onChange={handleInputChange}
                        min="0"
                        step="0.1"
                      />
                    </div>
                    <div className="form-group">
                      <label>Vitamin B1 (mg)</label>
                      <input
                        type="number"
                        name="vitaminB1"
                        value={mealForm.vitaminB1}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                      />
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label>Vitamin B2 (mg)</label>
                      <input
                        type="number"
                        name="vitaminB2"
                        value={mealForm.vitaminB2}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                      />
                    </div>
                    <div className="form-group">
                      <label>Vitamin B3 (mg)</label>
                      <input
                        type="number"
                        name="vitaminB3"
                        value={mealForm.vitaminB3}
                        onChange={handleInputChange}
                        min="0"
                        step="0.1"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Dietary Tags */}
              <div className="form-section full-width">
                <h3>Dietary Tags</h3>
                <div className="checkbox-group">
                  {commonDietaryTags.map(tag => (
                    <label
                      key={tag}
                      className={`checkbox-label ${
                        tag === 'Other'
                          ? (showCustomDietaryInput ? 'checked' : '')
                          : (mealForm.dietaryTags.includes(tag) ? 'checked' : '')
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={
                          tag === 'Other'
                            ? showCustomDietaryInput
                            : mealForm.dietaryTags.includes(tag)
                        }
                        onChange={() => handleMultiSelect('dietaryTags', tag)}
                      />
                      <span>{tag}</span>
                    </label>
                  ))}
                </div>

                {/* Custom Dietary Tag Input */}
                {showCustomDietaryInput && (
                  <div className="custom-input-container" style={{backgroundColor: '#f0f0f0', padding: '10px', margin: '10px 0', border: '2px solid #20C5AF'}}>
                    <input
                      type="text"
                      value={customDietaryInput}
                      onChange={(e) => setCustomDietaryInput(e.target.value)}
                      placeholder="Enter custom dietary tag (separate multiple with commas)"
                      className="custom-input"
                      onKeyDown={(e) => e.key === 'Enter' && addCustomDietaryTag()}
                      style={{flex: 1, padding: '8px', marginRight: '8px'}}
                    />
                    <button
                      type="button"
                      onClick={addCustomDietaryTag}
                      className="add-custom-btn"
                      style={{padding: '8px 16px', backgroundColor: '#20C5AF', color: 'white', border: 'none', borderRadius: '4px'}}
                    >
                      Add
                    </button>
                  </div>
                )}


              </div>

              {/* Diet Type */}
              <div className="form-section full-width">
                <h3>Diet Type</h3>
                <div className="checkbox-group">
                  {commonDietaryTags.map(tag => {
                    const key = tagToDietTypeKey(tag);
                    return (
                      <label
                        key={key}
                        className={`checkbox-label ${
                          tag === 'Other'
                            ? (showCustomDietTypeInput ? 'checked' : '')
                            : (mealForm.dietType[key] ? 'checked' : '')
                        }`}
                      >
                        <input
                          type="checkbox"
                          checked={
                            tag === 'Other'
                              ? showCustomDietTypeInput
                              : mealForm.dietType[key]
                          }
                          onChange={() => handleDietTypeSelect(tag)}
                        />
                        <span>{tag}</span>
                      </label>
                    );
                  })}
                </div>

                {/* Custom Diet Type Input */}
                {showCustomDietTypeInput && (
                  <div className="custom-input-container" style={{backgroundColor: '#f0f0f0', padding: '10px', margin: '10px 0', border: '2px solid #20C5AF'}}>
                    <input
                      type="text"
                      value={customDietTypeInput}
                      onChange={(e) => setCustomDietTypeInput(e.target.value)}
                      placeholder="Enter custom diet type (separate multiple with commas)"
                      className="custom-input"
                      onKeyDown={(e) => e.key === 'Enter' && addCustomDietType()}
                      style={{flex: 1, padding: '8px', marginRight: '8px'}}
                    />
                    <button
                      type="button"
                      onClick={addCustomDietType}
                      className="add-custom-btn"
                      style={{padding: '8px 16px', backgroundColor: '#20C5AF', color: 'white', border: 'none', borderRadius: '4px'}}
                    >
                      Add
                    </button>
                  </div>
                )}
              </div>

              {/* Allergens */}
              <div className="form-section full-width">
                <h3>Allergens</h3>
                <div className="checkbox-group">
                  {commonAllergens.map(allergen => (
                    <label
                      key={allergen}
                      className={`checkbox-label ${
                        allergen === 'Other'
                          ? (showCustomAllergenInput ? 'checked' : '')
                          : (mealForm.allergens.includes(allergen) ? 'checked' : '')
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={
                          allergen === 'Other'
                            ? showCustomAllergenInput
                            : mealForm.allergens.includes(allergen)
                        }
                        onChange={() => handleMultiSelect('allergens', allergen)}
                      />
                      <span>{allergen}</span>
                    </label>
                  ))}
                </div>

                {/* Custom Allergen Input */}
                {showCustomAllergenInput && (
                  <div className="custom-input-container" style={{backgroundColor: '#f0f0f0', padding: '10px', margin: '10px 0', border: '2px solid #20C5AF'}}>
                    <input
                      type="text"
                      value={customAllergenInput}
                      onChange={(e) => setCustomAllergenInput(e.target.value)}
                      placeholder="Enter custom allergen (separate multiple with commas)"
                      className="custom-input"
                      onKeyDown={(e) => e.key === 'Enter' && addCustomAllergen()}
                      style={{flex: 1, padding: '8px', marginRight: '8px'}}
                    />
                    <button
                      type="button"
                      onClick={addCustomAllergen}
                      className="add-custom-btn"
                      style={{padding: '8px 16px', backgroundColor: '#20C5AF', color: 'white', border: 'none', borderRadius: '4px'}}
                    >
                      Add
                    </button>
                  </div>
                )}


              </div>

              {/* Ingredients */}
              <div className="form-section full-width">
                <h3>Ingredients</h3>
                <textarea
                  placeholder="Enter each ingredient on a new line"
                  value={mealForm.ingredients.join('\n')}
                  onChange={(e) => handleArrayInput('ingredients', e.target.value)}
                  rows="8"
                  className="array-input"
                />
                <small>Enter each ingredient on a separate line</small>
              </div>

              {/* Instructions */}
              <div className="form-section full-width">
                <h3>Instructions</h3>
                <textarea
                  placeholder="Enter each instruction step on a new line"
                  value={mealForm.instructions.join('\n')}
                  onChange={(e) => handleArrayInput('instructions', e.target.value)}
                  rows="10"
                  className="array-input"
                />
                <small>Enter each instruction step on a separate line</small>
              </div>

              {/* Form Actions */}
              <div className="form-actions">
                <button
                  type="button"
                  className="btn-cancel"
                  onClick={() => {
                    setShowMealForm(false);
                    resetForm();
                    setActiveTab('view');
                  }}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn-save"
                  onClick={saveMeal}
                  disabled={!mealForm.name || !mealForm.category || mealForm.mealType.length === 0}
                >
                  <FaSave /> {editingMeal ? 'Update Meal' : 'Save Meal'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default MealManagement;
