@import url('https://fonts.googleapis.com/css2?family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&display=swap');

/* ===== DESIGN SYSTEM - CONSISTENT THEME ===== */
:root {
  /* Primary Colors */
  --primary-color: #20C5AF;
  --primary-hover: #1BA896;
  --primary-light: #E8F8F5;
  --primary-dark: #158B7A;

  /* Secondary Colors */
  --secondary-color: #2563EB;
  --secondary-hover: #1D4ED8;
  --secondary-light: #EFF6FF;

  /* Success Colors */
  --success-color: #10B981;
  --success-hover: #059669;
  --success-light: #ECFDF5;

  /* Warning Colors */
  --warning-color: #F59E0B;
  --warning-hover: #D97706;
  --warning-light: #FFFBEB;

  /* Error Colors */
  --error-color: #EF4444;
  --error-hover: #DC2626;
  --error-light: #FEF2F2;

  /* Neutral Colors */
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;

  /* Text Colors */
  --text-primary: #1F2937;
  --text-secondary: #6B7280;
  --text-light: #9CA3AF;

  /* Background Colors */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F9FAFB;
  --bg-tertiary: #F3F4F6;

  /* Border Colors */
  --border-light: #E5E7EB;
  --border-medium: #D1D5DB;
  --border-dark: #9CA3AF;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-full: 9999px;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;

  /* Typography */
  --font-family: 'Roboto Condensed', sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Landering Header */
.landing-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #20C5AF;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 15px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #333;
  font-weight: 700;
  font-size: 1.5rem;
}

.logo img {
  height: 40px;
  margin-right: 10px;
}

.main-nav ul {
  display: flex;
  list-style: none;
  align-items: center;
  font-family: "Roboto Condensed", sans-serif;
}

.main-nav ul li {
  margin-left: 25px;
}

.main-nav ul li a {
  color: #ffffff;
  text-decoration: none;
  font-weight: 500;
  /* Expanded transition to include all hover effects */
  transition: all 0.3s ease;
}

.main-nav ul li a:hover {
  color: #ffffff; /* Keeping your original hover color */
  transform: scale(1.05);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
  letter-spacing: 1.5px;
}

.btn-login {
  color: #ffffff !important;
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
}

.hamburger {
  display: block;
  width: 25px;
  height: 3px;
  background-color: #333;
  position: relative;
  transition: all 0.3s ease;
}

.hamburger:before,
.hamburger:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #333;
  transition: all 0.3s ease;
}

.hamburger:before {
  top: -8px;
}

.hamburger:after {
  top: 8px;
}

.hamburger.active {
  background-color: transparent;
}

.hamburger.active:before {
  transform: rotate(45deg);
  top: 0;
}

.hamburger.active:after {
  transform: rotate(-45deg);
  top: 0;
}



@media (max-width: 768px) {
  .mobile-menu-btn {
    display: block;
    z-index: 1001;
  }

  .main-nav {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    max-width: 300px;
    height: 100vh;
    background-color: white;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    padding: 80px 20px 20px;
    z-index: 1000;
    font-family: "Roboto Condensed", sans-serif;
  }
  
  .main-nav.active {
    right: 0;
  }

  .main-nav ul {
    flex-direction: column;
    align-items: flex-start;
  }

  .main-nav ul li {
    margin: 0 0 20px 0;
    width: 100%;
  }

  .main-nav ul li a {
    /* Base styles for nav links in mobile view */
    transition: all 0.3s ease;
  }

  .main-nav ul li a:hover {
    /* Same hover effect as desktop */
    color: #ffffff;
    transform: scale(1.05);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
    letter-spacing: 1.5px;
  }

  .main-nav ul li:last-child {
    margin-top: 20px;
  }

  .main-nav ul li a.btn {
    display: block;
    text-align: center;
    transition: all 0.3s ease;
  }

  .main-nav ul li a.btn:hover {
    /* Similar effect for buttons */
    transform: scale(1.05);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
    letter-spacing: 1.5px;
  }
}



/* Footer */
.site-footer {
  background-color: #333;
  color: #fff;
  padding: 60px 0 20px;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 40px;
}

.footer-section {
  flex: 1;
  min-width: 250px;
  margin-bottom: 30px;
  padding-right: 20px;
}

.footer-section h3 {
  font-size: 1.3rem;
  margin-bottom: 20px;
  color: #20c5af;
  position: relative;
  padding-bottom: 10px;
}

.footer-section h3:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: #20c5af;
}

.about p {
  line-height: 1.6;
  margin-bottom: 20px;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  display: inline-block;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: #fff;
  text-align: center;
  line-height: 36px;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background-color: #20c5af;
  transform: translateY(-3px);
}

.links ul {
  list-style: none;
  padding: 0;
}

.links ul li {
  margin-bottom: 12px;
}

.links ul li a {
  color: #ddd;
  text-decoration: none;
  transition: color 0.3s ease;
}

.links ul li a:hover {
  color: #20c5af;
  padding-left: 5px;
}

.contact p {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.contact p i {
  margin-right: 10px;
  color: #20c5af;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 20px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  font-size: 0.9rem;
}

.footer-bottom p {
  margin-bottom: 10px;
}

.footer-bottom-links {
  display: flex;
  gap: 20px;
}

.footer-bottom-links a {
  color: #ddd;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: #20c5af;
}

@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
  }
  
  .footer-section {
    margin-bottom: 40px;
  }
  
  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
  
  .footer-bottom-links {
    justify-content: center;
    margin-top: 10px;
  }
}

.landing-page {
  font-family: 'Roboto', sans-serif;
  color: #333;
}

.hero-section {
  background: var(--primary-color);
  background-size: cover;
  background-position: center;
  color: white;
  text-align: center;
  padding: 120px 20px;
  margin-top: 60px;
}

.hero-section h1 {
  font-size: 3.5rem;
  margin-bottom: 20px;
  font-weight: 700;
}

.tagline {
  font-size: 1.5rem;
  margin-bottom: 30px;
  font-weight: 300;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

.btn {
  padding: 12px 30px;
  border-radius: 30px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.btn-primary {
  background-color: #ff4f4f;
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: #ff5252;
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
}



.containerlanding {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.features-section, .how-it-works, .testimonials {
  margin-bottom: 80px;
}

h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 50px;
  color: #333;
  position: relative;
}

h2:after {
  content: '';
  display: block;
  width: 80px;
  height: 4px;
  background: #20c5af;
  margin: 15px auto 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-card {
  background: #fff;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #333;
}

.steps {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  counter-reset: step-counter;
}

.step {
  flex: 1;
  min-width: 200px;
  text-align: center;
  padding: 0 20px;
  position: relative;
  margin-bottom: 30px;
}

.step-number {
  width: 50px;
  height: 50px;
  background: #20c5af;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 1.5rem;
  font-weight: bold;
}

.step h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: #333;
}



@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 2.5rem;
  }
  
  .tagline {
    font-size: 1.2rem;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 80%;
    margin-bottom: 15px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .steps {
    flex-direction: column;
  }
    .step {
    margin-bottom: 40px;
  }
}




body, html {
  height: 100%;
  margin: 0;
  font-family: 'Arial', sans-serif;
}
/* Container */
.container {
  width: 100%;
  height: 100%;
  padding: 0; /* Remove horizontal padding */
}

/* Layout Structure */
.app-container {
  font-family: 'Inter', 'Roboto', Arial, sans-serif;
  background: #f7f8fa;
  min-height: 100vh;
  color: #222;
}


.content-area {
  flex: 1;
  margin-left: 250px; /* Same as sidebar width */
  padding-top: 90px; /* Space for header */
  transition: all 0.3s ease;
  min-width: 0; /* Prevents flex overflow issues */
  overflow-x: auto; /* Handle content that might be too wide */
}

/* Header */
.header {
  background-color: #70e4c4;
  width: 100%;
  height: 80px;
  padding: 15px 0;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left {
  display: flex;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: transform 0.2s ease;
  position: relative;
  left: 10px;
}

.logo {
  width: 75px;
  height: 50px;
  background-color: transparent;
  margin-right: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.logo-text-home{
  font-family: "Roboto Condensed", sans-serif;
  color: #ffffff;
  font-size: 35px;
  font-weight: bold;
  font-style: normal;
  font-optical-sizing: auto;
  letter-spacing: 0.5px;
}
.logo-text {
  font-family: "Roboto Condensed", sans-serif;
  color: #ffffff;
  font-size: 35px;
  font-weight: bold;
  font-style: normal;
  font-optical-sizing: auto;
  letter-spacing: 0.5px;
  
  /* Adding smooth transition for hover effects */
  transition: all 0.3s ease;
}

/* Hover effect - scale up slightly and add a subtle glow */
.logo-text:hover {
  transform: scale(1.05);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
  letter-spacing: 1.5px;
}

/* Sidebar Navigation */

/* Sidebar User Styling */
.sidebar-user {
  padding: 1.5rem 1rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.sidebar-user-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.sidebar-user-email {
  font-size: 0.9rem;
  color: #333;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* You can add this hover effect if you want */
.sidebar-user:hover {
  background-color: #eef7f2;
  transition: background-color 0.3s ease;
}

/* Optional: Add an avatar/profile picture placeholder */
.sidebar-user {
  position: relative;
  padding-left: 1rem; /* Adjust if adding an avatar */
}

/* If you want to match your existing sidebar styling better */
.sidebar-user {
  background-color: #f8fbf9; /* Light mint color to match the "Meal Plan" highlight */
}

.sidebar {
  width: 260px;
  background: var(--bg-primary);
  height: 100vh;
  position: fixed;
  left: 0;
  top: 70px; /* Below header */
  padding-top: 20px;
  border-right: 1px solid rgba(224, 224, 224, 0.8);
  box-shadow: 
    2px 0 10px rgba(0, 0, 0, 0.03),
    inset -1px 0 0 rgba(255, 255, 255, 0.9);
  z-index: 990;
  transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #d1d1d1 #f1f1f1;
}

.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
  background-color: #d1d1d1;
  border-radius: 10px;
  border: 2px solid #f1f1f1;
}

.sidebar-collapsed {
  width: 70px;
  padding-top: 15px;
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0 10px;
}

.sidebar-nav-item {
  position: relative;
  margin-bottom: 5px;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.sidebar-nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: var(--primary-color);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 3px;
}

.sidebar-nav-item:hover {
  background-color: rgba(112, 228, 196, 0.08);
}

.sidebar-nav-item a {
  display: flex;
  align-items: center;
  color: #424242;
  text-decoration: none;
  font-weight: 500;
  font-size: 15px;
  transition: all 0.3s;
  padding: 12px 15px;
  border-radius: 8px;
  position: relative;
  z-index: 1;
}

.sidebar-nav-item a:hover {
  color: #2c3e50;
  transform: translateX(5px);
}

.sidebar-nav-item .icon {
  margin-right: 12px;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #757575;
  transition: all 0.3s ease;
  position: relative;
}

.sidebar-nav-item:hover .icon {
  color: #70e4c4;
  transform: scale(1.1);
}

.sidebar-nav-item .nav-text {
  transition: opacity 0.3s, transform 0.3s;
}

.sidebar-collapsed .sidebar-nav-item .nav-text {
  opacity: 0;
  width: 0;
  transform: translateX(10px);
}

.sidebar-collapsed .sidebar-nav-item .icon {
  margin-right: 0;
}

.sidebar-collapsed .sidebar-nav-item a {
  justify-content: center;
  padding: 12px;
}

.sidebar-nav-item.active {
  background-color: rgba(112, 228, 196, 0.1);
}

.sidebar-nav-item.active::before {
  opacity: 1;
}

.sidebar-nav-item.active a {
  color: #2c3e50;
  font-weight: 600;
}

.sidebar-nav-item.active .icon {
  color: #70e4c4;
}

/* Tooltip for collapsed sidebar */
.sidebar-collapsed .sidebar-nav-item a {
  position: relative;
}

.sidebar-collapsed .sidebar-nav-item a::after {
  content: attr(data-title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
  white-space: nowrap;
  pointer-events: none;
  z-index: 1000;
  margin-left: 10px;
}

.sidebar-collapsed .sidebar-nav-item a::before {
  content: '';
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 5px solid transparent;
  border-right-color: #333;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
  pointer-events: none;
  z-index: 1000;
  margin-left: 0px;
}

.sidebar-collapsed .sidebar-nav-item a:hover::after,
.sidebar-collapsed .sidebar-nav-item a:hover::before {
  opacity: 1;
  visibility: visible;
}

.sidebar-section {
  margin-top: 25px;
  padding: 0 15px;
  position: relative;
}

.sidebar-section::before {
  content: '';
  position: absolute;
  top: -12px;
  left: 15px;
  right: 15px;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(224, 224, 224, 0.7), transparent);
}

.sidebar-section-title {
  font-size: 12px;
  color: #9e9e9e;
  margin-bottom: 15px;
  padding-left: 5px;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  font-weight: 600;
  transition: opacity 0.3s;
}

.sidebar-collapsed .sidebar-section-title {
  opacity: 0;
  height: 0;
  margin: 0;
  overflow: hidden;
}

/* Badge notifications */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  background-color: #ff5722;
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 0 6px;
  margin-left: auto;
  transition: all 0.3s;
}

.sidebar-collapsed .badge {
  position: absolute;
  top: 8px;
  right: 8px;
  transform: scale(0.8);
}

/* User profile section in sidebar */
.sidebar-profile {
  display: flex;
  align-items: center;
  padding: 15px;
  margin: 0 10px 20px;
  border-radius: 8px;
  background-color: rgba(224, 224, 224, 0.2);
  transition: all 0.3s ease;
}

.sidebar-profile:hover {
  background-color: rgba(224, 224, 224, 0.4);
}

.profile-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12px;
  border: 2px solid rgba(112, 228, 196, 0.5);
  transition: all 0.3s ease;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info {
  flex: 1;
  transition: opacity 0.3s, transform 0.3s;
}

.profile-name {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2px;
}

.profile-role {
  font-size: 12px;
  color: #757575;
}

.sidebar-collapsed .profile-info {
  opacity: 0;
  width: 0;
  transform: translateX(10px);
}

.sidebar-collapsed .sidebar-profile {
  justify-content: center;
}

.sidebar-collapsed .profile-avatar {
  margin-right: 0;
}

/* Toggle button for sidebar */
.sidebar-toggle {
  position: absolute;
  bottom: 20px;
  right: -15px;
  width: 30px;
  height: 30px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  z-index: 991;
  border: 1px solid rgba(224, 224, 224, 0.8);
}

.sidebar-toggle:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.sidebar-toggle .toggle-icon {
  transition: transform 0.3s ease;
  color: #757575;
}

.sidebar-collapsed .sidebar-toggle .toggle-icon {
  transform: rotate(180deg);
}

/* Dark mode toggle */
.dark-mode-toggle {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  margin: 10px;
  border-radius: 8px;
  background-color: rgba(224, 224, 224, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dark-mode-toggle:hover {
  background-color: rgba(224, 224, 224, 0.4);
}

.dark-mode-label {
  font-size: 14px;
  color: #424242;
  margin-left: 10px;
  transition: opacity 0.3s, transform 0.3s;
}

.dark-mode-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  margin-left: auto;
}

.switch-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  border-radius: 20px;
  transition: .4s;
}

.switch-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  border-radius: 50%;
  transition: .4s;
}

.switch-input:checked + .switch-slider {
  background: var(--primary-color);
}

.switch-input:checked + .switch-slider:before {
  transform: translateX(20px);
}

.sidebar-collapsed .dark-mode-label {
  opacity: 0;
  width: 0;
  transform: translateX(10px);
}

/* Responsive adjustments */
@media (max-width: 991px) {
  .sidebar {
    transform: translateX(-100%);
    width: 260px;
  }
  
  .sidebar.show {
    transform: translateX(0);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  }
  
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 989;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
  }
  
  .sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
  }
}

/* Animation for sidebar items */
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.sidebar-nav-item {
  animation: fadeInLeft 0.3s ease forwards;
  animation-delay: calc(0.05s * var(--item-index, 0));
  opacity: 0;
}

/* Active sidebar indication with notification dot */
.sidebar-nav-item.has-update::after {
  content: '';
  position: absolute;
  top: 12px;
  right: 15px;
  width: 8px;
  height: 8px;
  background-color: #ff5722;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 6px rgba(255, 87, 34, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 87, 34, 0);
  }
}

/* search bar */

/* Header Profile Section Styles */
.right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.icon-container:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* .heart-icon {
  width: 24px;
  height: 24px;
} */

.profile-dropdown {
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.profile-wrapper {
  display: flex;
  align-items: center;
  background-color: #65e0b8; /* Turquoise background from the image */
  padding: 8px 16px 8px 8px;
  border-radius: 30px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
}

.profile-wrapper:hover {
  background-color: #58c9a4;
}

.profile-pic {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 10px;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.profile-info {
  display: flex;
  align-items: center;
}

.profile-name {
  color: #333;
  font-size: 14px;
  font-weight: 500;
  margin-right: 6px;
}

.dropdown-arrow {
  width: 12px;
  height: 12px;
  transition: transform 0.3s ease;
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}


.dropdown-item {
  display: block;
  padding: 12px 16px;
  color: #333;
  text-decoration: none;
  font-size: 14px;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 6px 0;
}

.dropdown-item.logout {
  color: #333;

}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profile-name {
    display: none;
  }
  
  .dropdown-arrow {
    display: none;
  }
  
  .profile-wrapper {
    padding: 4px;
  }
  
  .dropdown-menu {
    right: -10px;
  }
}


/* Dropdown */
.dropContainer {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 100%;
  right: -8px;
  width: 130px;
  padding: 15px;
  border-radius: 15px;
  background-color: white;
  border: 2px solid rgba(0, 0, 0, 0.1);
  z-index: 2000;
  margin-top: 10px;
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.15);
}

.dropContainer::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 10px;
  width: 16px;
  height: 16px;
  background-color: white;
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  transform: rotate(45deg);
  z-index: 2000;
}

/* Dropdown list */
.dropList {
  display: flex;
  flex-direction: column;
  gap: 8px;
  list-style: none;
  align-items: center;
}

.dropList li {
  padding: 8px 10px;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.dropList li:hover {
  background-color: #f0f0f0;
  transform: translateX(3px);
}

.dropList li a {
  color: inherit; 
  text-decoration: none; 
}

.meal-range-label {
  margin-left: auto;
  font-size: 0.78em;
  font-weight: 500;
  color: #3b3b3b36;
  border-radius: 10px;
  padding: 2px 8px;
}

.dietary-badges {
  margin: 0.15rem 0 0.1rem 0;
  display: flex;
  flex-wrap: wrap;
  gap: 0.3em;
}

.dietary-badge {
color: white;
  background: #4caf50;
  color: #fff;
  border-radius: 14px;
  padding: 2px 8px;
  font-size: 0.68em;
  font-weight: 600;
  margin-top: 0;
  display: inline-block;
  margin-right: 0;
}

/* Main content area */
.main-content {
  margin: 0;
  padding: 24px;
  background-color: var(--main-bg-color);
  width: 100%;
  min-height: 100vh;
}

/* food card */
.suggested-dishes {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  padding-top: 10px;
  padding-bottom: 30px;
}

.dish-card {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 250px;
}

.dish-card img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 5px;
}

.dish-info {
  margin-top: 10px;
  text-align: center;
}



.recommended-dishes {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  padding-top: 20px;
  padding-bottom: 30px;
}

.recommended-dishes .dish-card {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 250px;
}

.recommended-dishes .dish-card img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 5px;
}

.recommended-dishes .dish-info {
  margin-top: 10px;
  text-align: center;
}

.recommended-dishes .rating {
  display: flex;
  justify-content: center;
}

.recommended-dishes .rating span {
  color: #1e1b10;
}

/* Mobile menu toggle */
.mobile-menu-toggle {
  display: none;
  cursor: pointer;
  margin-right: 15px;
}

.mobile-menu-toggle span {
  display: block;
  width: 25px;
  height: 3px;
  background-color: #333;
  margin: 5px 0;
  transition: all 0.3s ease;
}

.category-tag {
  background: #ffe0b2;
  color: #ff9800;
  border-radius: 8px;
  padding: 2px 10px;
  font-size: 0.93rem;
  font-weight: 500;
}

.dish-card {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.dish-card:hover {
  transform: translateY(-5px);
}

.suggested-dishes, .recommended-dishes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}


.dish-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

/* Filter tabs styling */
.filter-tabs {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-tab {
  background: #fff;
  border: none;
  border-radius: 999px;
  padding: 8px 20px;
  font-size: 1rem;
  color: #666;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.filter-tab.active {
  background: #ffb347;
  color: #fff;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(255,179,71,0.15);
}

/* .food-card-image {
  position: relative;
  height: 180px;
  overflow: hidden;
} */

/* .food-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.food-card:hover .food-card-image img {
  transform: scale(1.05);
} */

@media (min-width: 1200px) {
  .food-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 32px;
    padding: 0 32px;
  }
}

@media (max-width: 900px) {
  .food-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 600px) {
  .food-grid {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 1440px) {
  .food-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 36px;
    padding: 0 64px;
  }
}

@media (min-width: 1920px) {
  .food-grid {
    grid-template-columns: repeat(5, 1fr); /* 5 columns for more space */
    gap: 48px; /* More space between cards */
    padding: 0 120px;
  }
}

/* Enhanced Food Card Styling for Recently Viewed */
.food-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 0;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  min-width: 0;
  min-height: 380px;
  max-width: 360px;
  width: 100%;
  margin: 0 auto;
  position: relative;
  border: 2px solid #f1f5f9;
  overflow: hidden;
}

.food-card:hover {
  box-shadow: 0 8px 32px rgba(32, 197, 175, 0.2);
  transform: translateY(-6px);
  border-color: #20C5AF;
}

.food-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.food-card-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}
.food-card-tags {
  margin-bottom: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

/* .favorite-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
} */

.heart-icon {
  font-size: 1.2rem;
  color: #ccc;
  transition: color 0.3s ease;
}

.heart-icon:hover {
  color: #ff6b6b;
}

.heart-icon.filled {
  color: #e53935;
  animation: popHeart 0.22s;
}

@keyframes popHeart {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* .category-tag {
  display: inline-block;
  padding: 4px 8px;
  background-color: #f0f0f0;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-bottom: 10px;
} */

.rating {
  color: #ff9800;
  font-weight: 500;
}


/* .view-meal-btn {
  background-color: #ff6b6b;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  margin-top: auto;
} */

.view-meal-btn:hover {
  background: linear-gradient(90deg, #ff9800 0%, #ffd699 100%);
  color: #fff;
}


.modal-header h2 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #2d3748;
}


.modal-body {
  padding: 0 20px;
}

@media (min-width: 768px) {
  .modal-body {
    flex-direction: row;
    gap: 30px;
  }
}

.meal-image {
  width: 100%;
  margin-bottom: 24px;
}

@media (min-width: 768px) {
  .meal-image {
    margin-bottom: 0;
  }
}

.meal-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.meal-details {
  flex: 2;
}

.meal-meta {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.6rem;
  font-size: 0.93em;
  margin-bottom: 0.1rem;
}

.meal-stats {
  display: flex;
  align-items: center;
  gap: 0.7em;
  color: #666;
}

.meal-description {
  font-size: 0.97em;
  color: #444;
  margin: 0.13rem 0 0.3rem 0;
  min-height: 36px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recommendation-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  padding: 0.8rem 0.9rem 1.1rem 0.9rem;
  transition: box-shadow 0.18s, transform 0.18s;
  display: flex;
  flex-direction: column;
  min-width: 0;
  min-height: 340px;
  max-width: 340px;
  margin: 0 auto;
  position: relative;
}

.recommendation-card:hover {
  box-shadow: 0 6px 24px rgba(0,0,0,0.13);
  transform: translateY(-3px) scale(1.025);
}

.meal-rating {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #744210;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.meal-category {
  background: linear-gradient(135deg, #4299e1, #3182ce);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}


.meal-ingredients, .meal-steps {
  margin-bottom: 32px;
}

.meal-ingredients h3, .meal-steps h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 8px;
}

.meal-ingredients ul, .meal-steps ol {
  padding-left: 0;
  counter-reset: step-counter;
}

.meal-ingredients li, .meal-steps li {
  list-style: none;
  counter-increment: step-counter;
  margin-bottom: 12px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  position: relative;
  padding-left: 60px;
  line-height: 1.5;
  color: #4a5568;
}

.meal-steps li::before {
  content: counter(step-counter);
  position: absolute;
  left: 16px;
  top: 16px;
  background: linear-gradient(135deg, #4299e1, #3182ce);
  color: white;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.9rem;
}

h1, h2 {
  color: #333;
  margin-bottom: 20px;
}

h2 {
  margin-top: 30px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
}

/* Price Range Dropdown Styles */
.filter-controls {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 18px;
  margin-bottom: 24px;
  background: #f1f3f6;
  padding: 16px 18px;
  border-radius: 14px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.filter-section {
  flex: 1 1 auto;
}

.filter-tabs {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}


.range-dropdown-button {
  background: #fff;
  border: none;
  border-radius: 999px;
  padding: 8px 20px;
  color: #666;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background 0.2s, color 0.2s;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.range-dropdown-button:hover {
  background-color: #e9e9e9;
}

.range-dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 150px;
  margin-top: 5px;
}

.range-dropdown-menu button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 10px 15px;
  border: none;
  background: none;
  cursor: pointer;
}

.range-dropdown-menu button:hover {
  background-color: #f5f5f5;
}

.food-card-price {
  margin-bottom: 8px;
}

.food-card-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 0.5rem 0;
  align-items: center;
}
.price-range {
  font-size: 1.08rem;
  font-weight: 600;
  color: #ff9800;
}



/* Dropdown styling - fixed with proper button styling */
.category-dropdown-container,
.range-dropdown-container {
  position: relative;
}

.category-dropdown-button,
.range-dropdown-button {
  background: #fff;
  border: none;
  border-radius: 999px;
  padding: 8px 20px;
  color: #666;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background 0.2s, color 0.2s;
}

.category-dropdown-button.active,
.range-dropdown-button.active {
  background: #ffb347;
  color: #fff;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(255,179,71,0.15);
}

.dropdown-menu {
  position: absolute;
  top: 110%;
  left: 0;
  min-width: 160px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 6px 24px rgba(0,0,0,0.12);
  z-index: 10;
  padding: 8px 0;
  animation: fadeIn 0.18s;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-8px);}
  to { opacity: 1; transform: translateY(0);}
}

/* For category dropdown specifically */
.category-dropdown-container .dropdown-menu {
  left: 0;
  right: auto;
}

/* For range dropdown specifically */
.range-dropdown-container .dropdown-menu {
  right: 0;
  left: auto;
}

.dropdown-menu button {
  width: 100%;
  background: none;
  border: none;
  padding: 10px 22px;
  text-align: left;
  font-size: 1rem;
  color: #333;
  cursor: pointer;
  border-radius: 8px;
  transition: background 0.18s;
}

.dropdown-menu button:hover {
  background-color: #f5f5f5;
}

.dropdown-menu button.active {
  background-color: #ffeeee;
  color: #ff6b6b;
  font-weight: 500;
}

/* Add a subtle divider between dropdown items */
.dropdown-menu button:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}



/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .filter-tabs {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 10px;
  }
  
  .range-dropdown-container {
    align-self: flex-end;
  }
}

/* Food grid and cards */
/* Enhanced Food Grid for Recently Viewed Meals */
.food-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 2rem;
  margin-top: 0;
  padding: 0;
  justify-items: center;
}

.no-results {
  grid-column: 1 / -1;
  text-align: center;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

/* Modal styling improvements */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
  padding-top: 80px; /* Add top padding to avoid header overlap */
}

.modal-content {
  background-color: white;
  border-radius: 10px;
  width: 90%;
  max-width: 700px;
  max-height: 80vh; /* Reduced from 90vh to 80vh */
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 0 20px;
  border-bottom: 1px solid #e5e5e5;
  margin-bottom: 16px;
}

@keyframes modalSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.close-modal {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #718096;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-modal:hover {
  color: #ff6b6b;
  background-color: #f7fafc;
}

/* Meal Plan Calendar Styles */

/* Header Section */
.meal-plan-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
}

.header-title h1 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-subtitle {
  margin: 0;
  color: #6c757d;
  font-size: 1rem;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.preferences-btn, .generate-plan-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preferences-btn {
  background: var(--gray-600);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: var(--font-family);
}

.preferences-btn:hover {
  background: var(--gray-700);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.generate-plan-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: var(--font-family);
}

.generate-plan-btn:hover {
  background: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Notifications Section */
.notifications-section {
  margin-bottom: 1.5rem;
}

.search-loading,
.search-error {
  margin: 18px 0;
  padding: 10px 18px;
  border-radius: 8px;
  font-size: 1.04rem;
}
.search-loading {
  background: #fffbe7;
  color: #ff9800;
}
.search-error {
  background: #ffeaea;
  color: #d32f2f;
}

.meal-plan-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.meal-price {
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-group label {
  font-weight: 500;
}

.days-select,
.dietary-select {
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #ddd;
  background-color: white;
  min-width: 150px;
}

.meal-plan-calendar {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 10px; /* slightly more space */
  font-size: 1.25rem;  /* bigger header */
}

.day-column {
  flex: 1;
  padding: 12px;
  text-align: center;
  border-right: 1px solid #e0e0e0;
}

.day-column:last-child {
  border-right: none;
}

.meal-type-header {
  min-width: 100px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
}

.day-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.calendar-body {
  background-color: white;
}

.meal-row {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
}

.meal-row:last-child {
  border-bottom: none;
}

.meal-type-cell {
  min-width: 100px;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  background-color: #f8f8f8;
  border-right: 1px solid #e0e0e0;
}

.meal-cell {
  flex: 1;
  padding: 12px;
  min-height: 100px;
  border-right: 1px solid #e0e0e0;
  position: relative;
}

.meal-cell:last-child {
  border-right: none;
}

.add-meal-btn {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: none;
  border: 2px dashed #ddd;
  border-radius: 6px;
  cursor: pointer;
  color: #888;
  transition: all 0.2s;
  padding: 10px;
}

.add-meal-btn:hover {
  border-color: #ff6b6b;
  color: #ff6b6b;
  background-color: #fff8f8;
}

.planned-meal {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
  border-radius: 6px;
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  position: relative;
}

.meal-name {
  font-size: 1.09rem;
  font-weight: 700;
  margin: 0 0 0.18rem 0;
  color: #232323;
  line-height: 1.18;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.meal-nutrition {
  margin-bottom: 32px;
}

.meal-nutrition h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 8px;
}

.meal-calories {
  font-size: 0.85em;
  color: #666;
  margin-bottom: 5px;
}

.meal-actions {
  display: flex;
  gap: 5px;
  margin-top: auto;
}

.edit-meal-btn,
.remove-meal-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s;
}

.edit-meal-btn {
  color: #4a90e2;
}

.edit-meal-btn:hover {
  background-color: #e6f0fa;
}

.remove-meal-btn {
  color: #e25c5c;
}

.remove-meal-btn:hover {
  background-color: #fae6e6;
}

.totals-row {
  background-color: #f5f5f5;
}

.total-cell {
  font-weight: 500;
  text-align: center;
}

.total-calories {
  color: #333;
  font-size: 0.95em;
}

/* Meal Selector Modal */
.meal-selector-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.meal-selector-modal {
  background-color: white;
  border-radius: 12px;
  width: 95%;
  max-width: 1400px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

/* Enhanced Search and Filter Section - Better Alignment */
.search-filter-section {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  align-items: stretch;
  padding: 24px;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 16px;
  border: 2px solid #e9ecef;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.search-container {
  flex: 2;
  position: relative;
}

/* Removed search icon as requested */

.meal-search {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  background: white;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.meal-search:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-container {
  flex: 1;
  min-width: 200px;
}

.dietary-filter {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dietary-filter:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-container {
  flex: 3;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.search-container .meal-search {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1.1rem;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-weight: 500;
}

.search-container .meal-search:focus {
  outline: none;
  border-color: #20C5AF;
  box-shadow: 0 0 0 4px rgba(32, 197, 175, 0.15);
  transform: translateY(-1px);
}

.search-container .meal-search::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.filter-container {
  flex: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.dietary-filter {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1.1rem;
  background: white;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-weight: 600;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 48px;
}

.dietary-filter:focus {
  outline: none;
  border-color: #20C5AF;
  box-shadow: 0 0 0 4px rgba(32, 197, 175, 0.15);
  transform: translateY(-1px);
}

/* Meals Grid Container */
.meals-grid-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

.meals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 0.5rem;
}

.meal-card-grid {
  background: white;
  border-radius: 12px;
  border: 2px solid #f1f5f9;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.meal-card-grid:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: #3182ce;
}

.meal-card-grid .meal-card-image {
  height: 160px;
  overflow: hidden;
  position: relative;
}

.meal-card-grid .meal-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.meal-card-grid:hover .meal-card-image img {
  transform: scale(1.05);
}

.meal-card-grid .meal-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  font-size: 3rem;
  color: #6c757d;
}

/* Enhanced Meal Card Content for Better Visibility */
.meal-card-grid .meal-card-content {
  padding: 1.25rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 140px;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
}

.meal-card-grid .meal-card-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.75rem 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.meal-card-grid .meal-card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.meal-card-grid .meal-card-calories {
  font-weight: 700;
  color: #20C5AF;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 4px;
}

.meal-card-grid .meal-card-calories::before {
  content: '🔥';
  font-size: 0.9rem;
}

.meal-card-grid .meal-card-category {
  background: linear-gradient(135deg, #20C5AF, #1ba896);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: capitalize;
  box-shadow: 0 2px 4px rgba(32, 197, 175, 0.2);
}

.meal-card-grid .meal-card-description {
  font-size: 0.9rem;
  color: #4a5568;
  line-height: 1.5;
  margin: 0 0 1rem 0;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid #20C5AF;
}

.meal-card-grid .meal-card-tags {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.meal-card-grid .meal-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: #f8f9fa;
  color: #6c757d;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.meal-card-grid .meal-tag.prep-time {
  background: #fff3cd;
  color: #856404;
}

.meal-card-grid .meal-tag.rating {
  background: #d1ecf1;
  color: #0c5460;
}

.meal-card-grid .meal-card-add-btn {
  width: 100%;
  padding: var(--space-3);
  background: var(--success-color);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  margin-top: auto;
  font-family: var(--font-family);
  transition: all 0.2s ease;
}

.meal-card-grid .meal-card-add-btn:hover {
  background: var(--success-hover);
  transform: none; /* Remove transform to prevent overlap */
  box-shadow: var(--shadow-md);
}

/* Simple meal card actions - no hover effects - Updated 2025-07-18 */
.meal-card-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  margin-top: auto;
  padding: 0;
  position: relative;
  z-index: 2;
}

.meal-card-actions .meal-card-add-btn {
  width: 100%;
  padding: 10px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin: 0;
  position: static;
  height: auto;
  transition: background-color 0.2s ease;
}

.meal-card-actions .meal-card-add-btn:hover {
  background-color: #218838;
  transform: none; /* Prevent transform conflicts */
}

/* Removed old small details button - now using eye icon over image */

/* Eye button positioned over meal image */
.meal-card-details-eye-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  background: rgba(0, 123, 255, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.meal-card-details-eye-btn:hover {
  background: rgba(0, 86, 179, 0.95);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

/* Tooltip for eye button */
.meal-card-details-eye-btn::after {
  content: "See details";
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 20;
}

.meal-card-details-eye-btn:hover::after {
  opacity: 1;
}

/* Ensure meal card image container is positioned relative */
.meal-card-grid .meal-card-image {
  position: relative;
}

/* Meal selector modal specific fixes */
.meal-selector-modal .meal-card-grid {
  position: relative;
  overflow: visible;
}

.meal-selector-modal .meal-card-content {
  padding-bottom: 0.5rem;
}

.meal-selector-modal .meal-card-actions {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #f1f5f9;
}

.meal-selector-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e5e5;
  background-color: white;
  flex-shrink: 0;
}

.meal-selector-modal .modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
}

.meal-selector-modal .close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.meal-selector-modal .close-btn:hover {
  color: #ff6b6b;
  background-color: #f7fafc;
}

.meal-selector-modal .modal-body {
  padding: 24px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.search-container {
  margin-bottom: 24px;
  flex-shrink: 0;
}

.meal-search {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;
  background-color: #f8f9fa;
}

.meal-search:focus {
  outline: none;
  border-color: #4299e1;
  background-color: white;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

/* Horizontal Meal Cards Container */
.meals-horizontal-container {
  flex: 1;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.meals-horizontal-scroll {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 8px 0 8px 0;
  flex: 1;
  align-items: flex-start;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

/* Hide default scrollbar */
.meals-horizontal-scroll::-webkit-scrollbar {
  display: none;
}

/* Custom Scrollbar */
.custom-scrollbar-container {
  margin-top: 16px;
  padding: 0 4px;
  position: relative;
}

.custom-scrollbar-track {
  width: 100%;
  height: 12px;
  background: #e2e8f0;
  border-radius: 6px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #cbd5e0;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.custom-scrollbar-track:hover {
  background: #cbd5e0;
  border-color: #a0aec0;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.15);
}

.custom-scrollbar-thumb {
  position: absolute;
  top: 0;
  height: 100%;
  background: linear-gradient(135deg, #4299e1, #3182ce);
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s ease;
  min-width: 30px;
  border: 1px solid #2c5282;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #3182ce, #2c5282);
  transform: scaleY(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.custom-scrollbar-thumb.dragging {
  cursor: grabbing;
  background: linear-gradient(135deg, #2c5282, #2a4365);
  transform: scaleY(1.2);
  box-shadow: 0 4px 12px rgba(44, 82, 130, 0.4);
}

.custom-scrollbar-thumb::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 3px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}



/* Enhanced scrollbar visibility */
.meals-horizontal-container:hover .custom-scrollbar-track {
  background: #cbd5e0;
  border-color: #a0aec0;
}

.meals-horizontal-container:hover .custom-scrollbar-thumb {
  background: linear-gradient(135deg, #3182ce, #2c5282);
}

/* Touch-friendly scrollbar */
@media (hover: none) and (pointer: coarse) {
  .custom-scrollbar-track {
    height: 14px;
    background: #cbd5e0;
    border-color: #a0aec0;
  }

  .custom-scrollbar-thumb {
    min-width: 40px;
    background: linear-gradient(135deg, #3182ce, #2c5282);
  }
}

/* Smooth scrolling for all browsers */
.meals-horizontal-scroll {
  scroll-behavior: smooth;
}

/* Individual Meal Card */
.meal-card {
  flex: 0 0 280px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  height: fit-content;
  max-height: 420px;
  border: 1px solid #e2e8f0;
}

.meal-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #4299e1;
}

.meal-card-image {
  width: 100%;
  height: 160px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.meal-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.meal-card:hover .meal-card-image img {
  transform: scale(1.05);
}

.meal-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.meal-card-content {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.meal-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.meal-card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.meal-card-calories {
  font-weight: 600;
  color: #e53e3e;
  font-size: 0.9rem;
  background: #fed7d7;
  padding: 2px 8px;
  border-radius: 12px;
}

.meal-card-category {
  font-size: 0.8rem;
  color: #718096;
  background: #edf2f7;
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: capitalize;
}

.meal-card-description {
  font-size: 0.85rem;
  color: #4a5568;
  line-height: 1.4;
  margin: 8px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.meal-card-tags {
  display: flex;
  gap: 6px;
  margin-top: auto;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.meal-tag {
  font-size: 0.75rem;
  padding: 3px 8px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 3px;
  font-weight: 500;
}

.meal-tag.prep-time {
  background: #e6fffa;
  color: #234e52;
}

.meal-tag.rating {
  background: #fef5e7;
  color: #744210;
}

/* Commented out conflicting absolute positioned button style
.meal-card-add-btn {
  position: absolute;
  bottom: 16px;
  right: 16px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: var(--success-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(56, 161, 105, 0.3);
  font-size: 1.1rem;
}

.meal-card-add-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(56, 161, 105, 0.4);
  background: var(--success-hover);
}

.meal-card-add-btn:active {
  transform: scale(0.95);
}
*/

/* No meals found message */
.no-meals-found {
  text-align: center;
  padding: 40px 20px;
  color: #718096;
}

.no-meals-found p {
  font-size: 1.1rem;
  margin: 0;
}

/* Responsive Design for Meal Selector */
@media (max-width: 768px) {
  .meal-selector-modal {
    width: 98%;
    max-height: 90vh;
    margin: 10px;
  }

  .meal-selector-modal .modal-header {
    padding: 16px 20px;
  }

  .meal-selector-modal .modal-header h2 {
    font-size: 1.3rem;
  }

  .meal-selector-modal .modal-body {
    padding: 20px;
  }

  .meal-card {
    flex: 0 0 240px;
    max-height: 380px;
  }

  .meal-card-image {
    height: 140px;
  }

  .meal-card-content {
    padding: 14px;
  }

  .meal-card-title {
    font-size: 1rem;
  }

  .meal-card-description {
    font-size: 0.8rem;
    -webkit-line-clamp: 2;
  }
}

@media (max-width: 480px) {
  .meal-selector-modal {
    width: 100%;
    height: 100%;
    max-height: 100vh;
    border-radius: 0;
  }

  .search-filter-section {
    flex-direction: column;
    gap: 1rem;
    padding: 16px;
  }

  .search-container,
  .filter-container {
    flex: 1;
  }

  .search-container .meal-search,
  .dietary-filter {
    padding: 14px 16px;
    font-size: 1rem;
  }

  .meal-card {
    flex: 0 0 200px;
    max-height: 340px;
  }

  .meal-card-image {
    height: 120px;
  }

  .meal-card-content {
    padding: 12px;
  }

  /* Commented out responsive absolute positioned button
  .meal-card-add-btn {
    width: 36px;
    height: 36px;
    bottom: 12px;
    right: 12px;
    font-size: 1rem;
  }
  */

  .custom-scrollbar-track {
    height: 10px;
  }

  .custom-scrollbar-thumb {
    min-width: 25px;
  }

  .custom-scrollbar-thumb::before {
    width: 12px;
    height: 2px;
  }
}

.meals-grid-container {
  width: 100%;
  padding: 15px;
  overflow-y: auto;
  max-height: 70vh;
}

.meals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  width: 100%;
}

.meal-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  background-color: #fff;
  cursor: pointer;
}

.meal-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.meal-card-image {
  height: 150px;
  overflow: hidden;
}

.meal-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.meal-card-content {
  padding: 15px;
}

.meal-card-content h3 {
  margin: 0 0 10px;
  font-size: 16px;
}

.meal-card-meta {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
}

/* Enhanced History Page Styles */
.history-section {
  margin-bottom: 48px;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.history-section h2 {
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 3px solid #20C5AF;
  display: flex;
  align-items: center;
  gap: 12px;
}

.history-section h2::before {
  content: '🕒';
  font-size: 1.5rem;
}

/* Enhanced Meal Plan Card */
.meal-plan-card {
  position: relative;
}

.meal-plan-info {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 0 0 16px 16px;
  border-top: 2px solid #e9ecef;
}

.meal-plan-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.meal-type-badge {
  background: var(--secondary-color);
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: var(--font-family);
}

.added-date {
  font-size: 0.8rem;
  color: #718096;
  font-style: italic;
}

.planned-for {
  font-size: 0.85rem;
  color: #4a5568;
  font-weight: 500;
  background: #f7fafc;
  padding: 6px 10px;
  border-radius: 8px;
  border-left: 3px solid #4299e1;
}

/* Different colors for different meal types */
.meal-type-badge.meal-type-breakfast {
  background: var(--warning-color);
}

.meal-type-badge.meal-type-lunch {
  background: var(--success-color);
}

.meal-type-badge.meal-type-dinner {
  background: #9F7AEA;
}

.meal-type-badge.meal-type-viewed {
  background: var(--secondary-color);
}



/* Enhanced Responsive styles for History page */
@media (max-width: 768px) {
  .history-section {
    padding: 20px;
    margin-bottom: 36px;
  }

  .history-section h2 {
    font-size: 1.5rem;
    margin-bottom: 20px;
    padding-bottom: 12px;
  }

  .food-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }

  .meal-plan-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .meal-type-badge {
    font-size: 0.8rem;
    padding: 6px 12px;
  }

  .added-date {
    font-size: 0.8rem;
  }

  .planned-for {
    font-size: 0.85rem;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .history-section {
    padding: 16px;
    margin-bottom: 32px;
  }

  .history-section h2 {
    font-size: 1.3rem;
    margin-bottom: 18px;
    padding-bottom: 10px;
  }

  .food-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .food-card {
    max-width: 100%;
  }

  .meal-plan-info {
    margin-top: 12px;
    padding: 12px;
  }
}



.currently-selected {
  margin-top: 8px;
  font-size: 0.85em;
  color: #4caf50;
  display: flex;
  align-items: center;
  gap: 5px;
}

.no-meals-found {
  grid-column: 1 / -1;
  text-align: center;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  color: #666;
}

/* Calendar Navigation */
.calendar-navigation {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 16px;
  border: 1px solid #ddd;
  background-color: #f8f8f8;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.nav-button:hover {
  background-color: #f0f0f0;
  border-color: #ccc;
}

.nav-button.current-week {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

/* Today and Past Date Styling */
.day-column.today {
  background-color: #e6f7ff;
}

.day-column.today .day-name,
.day-column.today .day-number {
  color: #1890ff;
  font-weight: bold;
}

.day-column.past-date {
  background-color: #f5f5f5;
}

.meal-cell.past-date {
  background-color: #f9f9f9;
  color: #999;
  position: relative;
}

.meal-cell.past-date::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.5);
  pointer-events: none;
}

.meal-cell.past-date .planned-meal {
  opacity: 0.7;
}

.total-cell.past-date {
  background-color: #f9f9f9;
  color: #999;
}

/* Calendar-based Meal Plan Styles */
.monthly-calendar {
  margin-bottom: 30px;
}

.month-title {
  text-align: center;
  margin-bottom: 15px;
  font-size: 1.5rem;
  color: #333;
}

/* Full width calendar container */
.calendar-grid-full-width {
  width: 100%;
  padding: 0 20px;
  margin: 20px 0;
}

.calendar-grid {
  width: 100%;
  max-width: none;
  background: #f1f5f9;
  border-radius: 10px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 28px 20px 36px 20px; /* more padding */
}

.calendar-day-name {
  text-align: center;
  font-weight: bold;
  padding: 10px;
  background-color: #e0e0e0;
  border-radius: 4px;
}

.calendar-day {
  background: #fff;
  border-radius: 12px;
  min-height: 140px;
  padding: 12px;
  text-align: left;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.day-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
}

.lock-icon {
  color: #f39c12;
  font-size: 0.9rem;
}

.meal-indicators {
  display: flex;
  gap: 4px;
  margin: 8px 0;
  flex-wrap: wrap;
}

.meal-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.meal-indicator.has-meals {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  color: #1976d2;
  border-color: #2196f3;
}

.meal-indicator.breakfast.has-meals {
  background: linear-gradient(135deg, #fff3e0, #ffcc02);
  color: #f57c00;
  border-color: #ff9800;
}

.meal-indicator.lunch.has-meals {
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
  color: #388e3c;
  border-color: #4caf50;
}

.meal-indicator.dinner.has-meals {
  background: linear-gradient(135deg, #fce4ec, #f8bbd9);
  color: #c2185b;
  border-color: #e91e63;
}



.meal-type-initial {
  font-weight: 700;
}

.meal-count-badge {
  background: rgba(255, 255, 255, 0.8);
  color: inherit;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.6rem;
  font-weight: 700;
}

.day-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.7rem;
  color: #6c757d;
  margin-top: auto;
  padding-top: 4px;
  border-top: 1px solid #f1f3f4;
}

.total-meals {
  font-weight: 600;
}

.total-calories {
  font-weight: 500;
  color: #28a745;
}

.calendar-day:hover {
  background-color: #f9f9f9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.calendar-day.empty-day {
  background-color: transparent;
  border: none;
  cursor: default;
}

.calendar-day.empty-day:hover {
  transform: none;
  box-shadow: none;
}

.calendar-day.today {
  border: 2px solid #3182ce;
}

.calendar-day.past-date {
  background-color: #f9f9f9;
  color: #999;
}

.calendar-day.selected {
  border: 2px solid #38a169;
  box-shadow: 0 2px 8px rgba(56,161,105,0.08);
}

.calendar-day .day-number {
  font-size: 1.7rem;    /* was 1.1rem */
  font-weight: 700;
  color: #2d3748;
}

.meal-indicator {
  display: flex;
  flex-direction: column;
  font-size: 0.8rem;
}

.meal-count {
  font-size: 1.1rem;    /* was 0.85rem */
  color: #718096;
  margin-top: 6px;
}

.calorie-count {
  color: #52c41a;
}

/* Selected Date Meal Details */
.selected-date-meals {
  margin-top: 30px;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e0e0e0;
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.date-header h3 {
  margin: 0;
  color: #333;
}

.total-calories {
  font-weight: bold;
  color: #52c41a;
}

/* Meal type sections */
.meal-types-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.meal-type-section {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.meal-type-header-with-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f5f5;
  padding: 12px 15px;
  border-bottom: 1px solid #e0e0e0;
}

.meal-type-header-with-stats h4 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.meal-type-calories {
  font-weight: 500;
  color: #ff7a45;
}

.meal-items-container {
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.meal-item-card {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background-color: white;
  transition: all 0.2s;
  min-height: 80px;
}

.meal-item-card:hover {
  background-color: #f9f9f9;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.meal-item-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 15px;
  flex-shrink: 0;
}

.meal-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.meal-item-details {
  flex: 1;
}

.meal-item-name {
  margin: 0 0 5px 0;
  font-size: 1rem;
}

.meal-item-calories {
  color: #ff7a45;
  font-weight: 500;
  margin-bottom: 3px;
}

.meal-item-category {
  font-size: 0.8rem;
  color: #666;
}

.meal-item-actions {
  display: flex;
  gap: 8px;
  margin-left: 10px;
}

.no-meals-message {
  color: #999;
  font-style: italic;
  padding: 15px 0;
  text-align: center;
}

.add-meal-btn {
  width: 100%;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: none;
  border: 2px dashed #ddd;
  cursor: pointer;
  color: #888;
  transition: all 0.2s;
  border-radius: 6px;
  margin-top: 10px;
}

.add-meal-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
  background-color: #f0f7ff;
}

.remove-meal-btn {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: none;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.remove-meal-btn:hover {
  background-color: #ff4d4f;
  color: white;
}

/* Calendar Navigation */
.calendar-navigation {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 16px;
  border: 1px solid #ddd;
  background-color: #f8f8f8;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.nav-button:hover {
  background-color: #f0f0f0;
  border-color: #ccc;
}

.nav-button.current-month {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

/* Meal Selector Modal */
.meal-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.meal-selector-modal .modal-content {
  background-color: white;
  border-radius: 10px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.meal-selector-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 1;
}

.meal-selector-modal .close-modal {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.meal-selector-modal .close-modal:hover {
  color: #ff4d4f;
}

.meal-selector-modal .modal-body {
  padding: 20px;
}

.search-container {
  margin-bottom: 20px;
}

.meal-search {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
}

.meals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.meal-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s;
  cursor: pointer;
  background-color: white;
}

.meal-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-color: #1890ff;
}

.meal-card-image {
  height: 120px;
  overflow: hidden;
}

.meal-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.meal-card-content {
  padding: 12px;
}

.meal-card-content h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  line-height: 1.3;
}

.meal-card-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.85em;
  color: #666;
}

.no-meals-found {
  grid-column: 1 / -1;
  text-align: center;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  color: #666;
}


/* Add this to your existing Navbar.css */
.admin-link a {
  color: #65e0b8; /* Matching the header color */
  font-weight: 500;
}

.admin-link a:hover {
  color: #ffd633;
}

/* Responsive styles */
@media (max-width: 768px) {
  .calendar-navigation {
    flex-wrap: wrap;
    justify-content: center;
  }

  .nav-button {
    flex: 1;
    justify-content: center;
    min-width: 120px;
  }

  .calendar-grid-full-width {
    padding: 0 10px;
  }

  .calendar-grid {
    grid-template-columns: repeat(7, 1fr);
    font-size: 0.9rem;
    padding: 20px 15px 25px 15px;
  }

  .calendar-day {
    min-height: 80px;
  }
}


@media (max-width: 480px) {
  .calendar-grid-full-width {
    padding: 0 5px;
  }

  .calendar-grid {
    padding: 15px 10px 20px 10px;
  }

  .calendar-day {
    min-height: 60px;
    padding: 5px;
  }

  .calendar-day .day-number {
    font-size: 0.9rem;
  }

  .meal-indicator {
    font-size: 0.7rem;
  }
  
  .meals-grid {
    grid-template-columns: 1fr;
  }
  
  .meal-item-card {
    flex-direction: column;
    align-items: flex-start;
  }

  .meal-item-image {
    width: 100%;
    height: 120px;
    margin-right: 0;
    margin-bottom: 10px;
  }

  .meal-item-actions {
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
    justify-content: flex-end;
  }
}

/* Lock/Edit Functionality Styles */
.locked-date {
  background-color: #fff7e6 !important;
  border: 1px solid #ffd591 !important;
}

.lock-icon {
  position: absolute;
  top: 6px;
  right: 6px;
  color: #718096;
  font-size: 1rem;
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
  flex-wrap: wrap;
  gap: 10px;
}

.date-title-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-title-section h3 {
  margin: 0;
  color: #333;
}

.date-status {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.locked-status {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #fa8c16;
  background-color: #fff7e6;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.editable-status {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #52c41a;
  background-color: #f6ffed;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.date-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.save-plan-btn, .edit-plan-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.save-plan-btn {
  background-color: #52c41a;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.save-plan-btn:hover {
  background-color: #389e0d;
}

.edit-plan-btn {
  background-color: #faad14;
  color: white;
  border: none;
}

.edit-plan-btn:hover {
  background-color: #d48806;
}

.success-message {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 10px 15px;
  margin: 15px 0;
  color: #52c41a;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-in-out;
}

/* Profile Page Styles */

.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  color: #333;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eaeaea;
}

.profile-title h1 {
  font-size: 2rem;
  margin: 0;
  color: #2c3e50;
}

.profile-title p {
  margin: 0.5rem 0 0;
  color: #7f8c8d;
  font-size: 1rem;
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #333;
  color: #e74c3c;
  border: 1px solid #e74c3c;
  padding: 0.6rem 1.2rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-btn:hover {
  background-color: #e74c3c;
  color: white;
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 1.5rem;
  border: 4px solid #f8f9fa;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #3498db;
  color: white;
  font-size: 2.5rem;
  font-weight: bold;
}

.profile-name1 {
  text-align: center;
  margin-bottom: 2rem;
}

.profile-name h2 {
  margin: 0;
  font-size: 1.8rem;
  color: #2c3e50;
}

.profile-role {
  display: inline-block;
  background-color: #3498db;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.profile-details {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.detail-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-group h3 {
  font-size: 1.2rem;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eaeaea;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.detail-icon {
  color: #3498db;
  font-size: 1.2rem;
  min-width: 24px;
}

.detail-label {
  display: block;
  font-size: 0.85rem;
  color: #7f8c8d;
  margin-bottom: 0.2rem;
}

.detail-value {
  display: block;
  font-weight: 500;
  color: #2c3e50;
}

.profile-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 1rem;
}

.edit-profile-btn {
  background-color: #3498db;
  color: white;
}

.edit-profile-btn:hover {
  background-color: #2980b9;
}

.change-password-btn {
  background-color: #f8f9fa;
  color: #2c3e50;
  border: 1px solid #ddd;
}

.change-password-btn:hover {
  background-color: #ecf0f1;
}

.add-family-btn {
  background-color: #4caf50;
  color: white;
}

.add-family-btn:hover {
  background-color: #45a049;
}

.profile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 1rem;
  color: #7f8c8d;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
}

/* Add these styles to your existing App.css file */

/* Missed Meal Plan Notification */
.missed-plan-notification {
  display: flex;
  align-items: center;
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 20px;
  animation: fadeIn 0.3s ease-in-out;
}

.missed-plan-notification .warning-icon {
  color: #e0a800;
  font-size: 20px;
  margin-right: 10px;
}

.missed-plan-notification p {
  flex-grow: 1;
  margin: 0;
  color: #856404;
}

.view-missed-plan-btn {
  background: #3182ce;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 4px 10px;
  margin-left: 10px;
  cursor: pointer;
}

.dismiss-notification-btn {
  background: none;
  border: none;
  color: #856404;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
}

/* Meal Time Alerts */
.meal-time-alerts {
  margin-bottom: 20px;
}

.meal-time-alert {
  display: flex;
  align-items: center;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 10px;
  animation: slideIn 0.3s ease-in-out;
}

.meal-time-alert .alert-icon {
  color: #155724;
  font-size: 20px;
  margin-right: 10px;
}

.meal-time-alert p {
  flex-grow: 1;
  margin: 0;
  color: #155724;
}

.view-meal-btn {
  margin-top: auto;
  background: linear-gradient(90deg, #ffb347 0%, #ffcc80 100%);
  color: #232323;
  border: none;
  border-radius: 18px;
  padding: 7px 0;
  font-size: 1em;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  box-shadow: 0 1px 4px rgba(0,0,0,0.06);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5em;
}

/* Add to Meal Plan Button Styling */
.add-to-meal-plan-btn {
  width: 100%;
  background: linear-gradient(135deg, #20C5AF, #17a2b8);
  color: white;
  border: none;
  border-radius: 18px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(32, 197, 175, 0.2);
  margin-top: 8px;
}

.add-to-meal-plan-btn:hover {
  background: linear-gradient(135deg, #1ba896, #138496);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(32, 197, 175, 0.3);
}

.add-to-meal-plan-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(32, 197, 175, 0.2);
}

.dismiss-alert-btn {
  background: none;
  border: none;
  color: #155724;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
}

/* Calendar Day Styles */
.calendar-day.missed-plan {
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.missed-icon {
  color: #e0a800;
  font-size: 12px;
  margin-left: 4px;
  vertical-align: middle;
}

/* Meal Type Header with Time */
.meal-type-title-and-time {
  display: flex;
  align-items: center;
  gap: 15px;
}

.meal-time-setting {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.clock-icon {
  margin-right: 5px;
  font-size: 14px;
}

.time-input {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 2px 5px;
  font-size: 14px;
}

.time-display {
  font-weight: 500;
}

/* Missed Meal Indicator */
.meal-item-card.missed-meal {
  position: relative;
  border-left: 3px solid #e0a800;
}

.missed-meal-indicator {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: #fff3cd;
  color: #856404;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.missed-plan-badge {
  display: inline-flex;
  align-items: center;
  background-color: #fff3cd;
  color: #856404;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 4px;
  margin-left: 10px;
  gap: 5px;
}

/* Completed Meal Styling */
.meal-item-card.completed-meal {
  border-left: 3px solid #28a745;
  background-color: rgba(40, 167, 69, 0.05);
}

.meal-completed-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-right: 10px;
  user-select: none;
}

.meal-completed-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: relative;
  height: 18px;
  width: 18px;
  background-color: #eee;
  border-radius: 3px;
  margin-right: 5px;
}

.meal-completed-checkbox:hover input ~ .checkmark {
  background-color: #ccc;
}

.meal-completed-checkbox input:checked ~ .checkmark {
  background-color: #28a745;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.meal-completed-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.meal-completed-checkbox .checkmark:after {
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-label {
  font-size: 12px;
  color: #666;
}


/* Search results styling - make sure these are properly defined */
.search {
  position: relative;
  width: 100%;
  max-width: 700px;
}

.search input {
  width: 100%;
  padding: 10px 15px;
  border-radius: 20px;
  border: 1px solid #ddd;
  outline: none;
  font-size: 14px;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 400px;
  overflow-y: auto;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 5px;
  border: 1px solid #eee;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
  text-decoration: none;
  color: #333;
  transition: background-color 0.2s;
  cursor: pointer;
}

.search-result-item:hover {
  background-color: #f9f9f9;
}

.search-result-image {
  width: 50px;
  height: 50px;
  margin-right: 15px;
  border-radius: 5px;
  overflow: hidden;
  flex-shrink: 0;
}

.search-result-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.search-result-info {
  flex-grow: 1;
  overflow: hidden;
}



.search-result-info h4 {
  margin: 0 0 5px 0;
  font-size: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.search-result-info p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

/* Food card actions styling */
.food-card-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.favorite-btn, .favorites-page-btn {
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.favorite-btn:hover, .favorites-page-btn:hover {
  transform: scale(1.1);
  background-color: white;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

/* Update existing food-card-image to position items properly */
.food-card-image {
  position: relative;
  width: 100%;
  aspect-ratio: 4/3;
  overflow: hidden;
}

.food-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.22s;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}

.food-card:hover .food-card-image img {
  transform: scale(1.05);
}

.food-card-content {
  padding: 18px 16px 16px 16px;
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.food-card-content h3 {
  font-size: 1.18rem;
  font-weight: 600;
  margin: 0 0 6px 0;
  color: #222;
}

.food-card-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.98rem;
  margin-bottom: 4px;
}

/* Notification styling */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #4CAF50;
  color: white;
  padding: 12px 24px;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  animation: fadeInOut 3s ease;
  font-weight: 500;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: translateY(-20px); }
  10% { opacity: 1; transform: translateY(0); }
  90% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-20px); }
}

/* Favorites page styling */
.no-favorites {
  text-align: center;
  padding: 40px 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin: 20px 0;
}

.no-favorites h2 {
  color: #333;
  margin-bottom: 10px;
}

.no-favorites p {
  color: #666;
  font-size: 16px;
}

/* Trash icon for removing favorites */
.trash-icon {
  color: #ff6b6b;
  font-size: 18px;
}

/* Update existing favorite-btn styling */
.favorite-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255,255,255,0.92);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.18rem;
  color: #e74c3c;
  cursor: pointer;
  box-shadow: 0 1px 4px rgba(0,0,0,0.08);
  transition: background 0.15s;
  z-index: 2;
}
.favorite-btn.favorited {
  background: #ffeaea;
}
.favorite-btn:hover {
  transform: scale(1.1);
  background-color: white;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

/* .heart-icon {
  color: #ff6b6b;
  font-size: 18px;
}

.heart-icon.filled {
  color: #ff6b6b;
} */


/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}


@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.profile-error {
  background-color: #fdecea;
  color: #e74c3c;
  padding: 1rem;
  border-radius: 4px;
  text-align: center;
  margin: 2rem 0;
}

/* Responsive adjustments */
/* Family Section Styles */
.family-section {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  margin-bottom: 2rem;
}

.family-header {
  margin-bottom: 2rem;
}

.family-title {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.family-icon {
  color: #4285f4;
  font-size: 1.5rem;
  margin-top: 0.2rem;
}

.family-title h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  color: #202124;
  font-weight: 400;
}

.family-title p {
  margin: 0;
  color: #5f6368;
  font-size: 0.9rem;
}

.learn-more {
  color: #1a73e8;
  text-decoration: none;
}

.learn-more:hover {
  text-decoration: underline;
}

.family-members {
  border: 1px solid #e8eaed;
  border-radius: 8px;
  overflow: hidden;
}

.family-member {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e8eaed;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.family-member:last-child {
  border-bottom: none;
}

.family-member:hover {
  background-color: #f8f9fa;
}

.family-member.current-user {
  background-color: #f8f9ff;
  border-left: 4px solid #4285f4;
}

.member-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 1rem;
  flex-shrink: 0;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-avatar .avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #4285f4;
  color: white;
  font-size: 1.2rem;
  font-weight: 500;
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: 1rem;
  color: #202124;
  font-weight: 500;
  margin-bottom: 0.2rem;
}

.member-role {
  font-size: 0.85rem;
  color: #5f6368;
}

.member-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.remove-member-btn {
  background: none;
  border: none;
  color: #5f6368;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  opacity: 0;
}

.family-member:hover .remove-member-btn {
  opacity: 1;
}

.remove-member-btn:hover {
  background-color: #fee;
  color: #d93025;
}

.member-arrow {
  color: #5f6368;
  font-size: 0.9rem;
}

.family-loading {
  padding: 2rem;
  text-align: center;
  color: #5f6368;
}

.no-family-members {
  padding: 2rem;
  text-align: center;
  color: #5f6368;
}

.add-member-section {
  padding: 1rem 1.5rem;
  background-color: #f8f9fa;
}

.add-member-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: 1px solid #dadce0;
  color: #1a73e8;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.add-member-btn:hover {
  background-color: #f1f3f4;
  border-color: #1a73e8;
}

.add-icon {
  font-size: 0.8rem;
}

.family-sharing {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e8eaed;
}

.family-sharing h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: #202124;
  font-weight: 500;
}

.family-sharing p {
  margin: 0;
  color: #5f6368;
  font-size: 0.9rem;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .profile-container {
    padding: 1rem;
  }

  .profile-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .logout-btn {
    align-self: flex-end;
  }

  .profile-details {
    grid-template-columns: 1fr;
  }

  .profile-actions {
    flex-direction: column;
    width: 100%;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }

  .family-section {
    padding: 1rem;
  }

  .family-title {
    flex-direction: column;
    gap: 0.5rem;
  }

  .family-member {
    padding: 1rem;
  }

  .member-avatar {
    width: 40px;
    height: 40px;
  }

  .add-member-section {
    padding: 1rem;
  }
}

/* Sidebar Recommendations Styles */
.recommendations-container {
  margin-top: 0.5rem;
}

.recommendations-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  color: #666;
  font-size: 0.85rem;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #20C5AF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.recommendation-item {
  display: flex;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid transparent;
}

.recommendation-item:hover {
  background: #e9ecef;
  border-color: #20C5AF;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(32, 197, 175, 0.1);
}

.recommendation-image {
  width: 100%;
  height: 145px;
  border-radius: 12px;
  overflow: hidden;
  background: #f6f6f6;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 0.7rem;
}

.recommendation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
}

.recommendation-placeholder {
  font-size: 1.5rem;
  color: #20C5AF;
}

.recommendation-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.recommendation-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: #333;
  margin-bottom: 0.25rem;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommendation-meta {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  flex-wrap: wrap;
}

.recommendation-type {
  background: #20C5AF;
  color: white;
  padding: 0.15rem 0.4rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: capitalize;
}

.recommendation-calories {
  background: #e9ecef;
  color: #666;
  padding: 0.15rem 0.4rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.recommendation-time {
  background: #fff3cd;
  color: #856404;
  padding: 0.15rem 0.4rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.recommendation-description {
  font-size: 0.75rem;
  color: #666;
  line-height: 1.3;
  margin-top: 0.25rem;
}

.recommendation-score {
  position: absolute;
  left: 10px;
  top: 10px;
  background: #fffbe7;
  color: #f39c12;
  border-radius: 14px;
  font-size: 0.92em;
  font-weight: 600;
  padding: 2px 9px 2px 7px;
  display: flex;
  align-items: center;
  gap: 3px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.07);
}

.view-all-recommendations {
  margin-top: 0.5rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

.view-all-link {
  color: #20C5AF;
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 500;
  transition: color 0.2s ease;
}

.view-all-link:hover {
  color: #1a9d8a;
  text-decoration: underline;
}

.no-recommendations {
  text-align: center;
  padding: 1.5rem 1rem;
  color: #666;
}

.no-recommendations-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.5;
}

.no-recommendations-text {
  font-size: 0.85rem;
  margin-bottom: 0.75rem;
}

.browse-meals-link {
  color: #20C5AF;
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border: 1px solid #20C5AF;
  border-radius: 6px;
  display: inline-block;
  transition: all 0.2s ease;
}

.browse-meals-link:hover {
  background: #20C5AF;
  color: white;
}

/* Responsive adjustments for recommendations */
@media (max-width: 768px) {
  .recommendation-item {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .recommendation-image {
    width: 40px;
    height: 40px;
  }

  .recommendation-name {
    font-size: 0.85rem;
  }

  .recommendation-type,
  .recommendation-calories,
  .recommendation-time {
    font-size: 0.65rem;
    padding: 0.1rem 0.3rem;
  }

  .recommendation-description {
    font-size: 0.7rem;
  }
}




@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .date-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .date-actions {
    width: 100%;
    justify-content: space-between;
    margin-top: 10px;
  }
}







/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-controls {
    flex-direction: column;
    gap: 10px;
  }
  
  .filter-tabs {
    width: 100%;
    overflow-x: auto;
  }
  
  .range-dropdown-container {
    width: 100%;
  }
  
  .range-dropdown-button {
    width: 100%;
    justify-content: space-between;
  }
}










/* Responsivenessations */
@media (max-width: 992px) {
  .sidebar {
    width: 200px;
  }
  .content-area {
    margin-left: 200px;
  }
  .suggested-dishes {
    grid-template-columns: repeat(3, 1fr);
  }
  .recommended-dishes {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }
  
  .sidebar {
    transform: translateX(-100%);
    width: 250px;
  }
  
  .sidebar.active {
    transform: translateX(0);
  }
  
  .content-area {
    margin-left: 0;
  }
  
  .search {
    max-width: 40%;
  }
  
  .logo-text {
    font-size: 20px;
  }
  
  .profile-pic {
    width: 28px;
    height: 28px;
  }
  
  .profile-name {
    font-size: 14px;
  }
  
  .heart-icon {
    width: 22px;
    height: 22px;
  }
  
  .suggested-dishes {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .recommended-dishes {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media (max-width: 576px) {
  .logo-text {
    font-size: 18px;
  }
  
  .search {
    max-width: 35%;
  }
  
  .search input {
    padding: 8px 12px;
    font-size: 14px;
  }
  
  .profile-name {
    display: none; 
  }
  
  .dropContainer {
    width: 120px;
    right: -10px;
  }
  
  .icon-badge {
    width: 16px;
    height: 16px;
    font-size: 10px;
  }
  
  .suggested-dishes {
    grid-template-columns: 1fr;
  }
}



/* Nutrition Grid */
.nutrition-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.nutrition-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #4299e1;
}

.nutrition-label {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.9rem;
}

.nutrition-value {
  font-weight: 700;
  color: #4299e1;
  font-size: 0.9rem;
}

/* Tags */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.dietary-tag, .meal-type-tag {
  background: linear-gradient(135deg, #9f7aea, #805ad5);
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: capitalize;
}

.dietary-tag {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: capitalize;
}

.meal-type-tag {
  background-color: #fff2e6;
  color: #ff6600;
}

/* Modal Footer */
.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #e5e5e5;
  display: flex;
  justify-content: center;
  gap: 16px;
}

.modal-footer-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.delete-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.delete-btn:hover {
  background-color: #c82333;
  transform: translateY(-1px);
}

.delete-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.favorite-modal-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #e53e3e, #c53030);
  color: white;
}

.favorite-modal-btn:hover {
  background: linear-gradient(135deg, #c53030, #9c2626);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

.favorite-modal-btn svg {
  color: #ff6b6b;
}

.meal-plan-modal-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #38a169, #2f855a);
  color: white;
}

.meal-plan-modal-btn:hover {
  background: linear-gradient(135deg, #2f855a, #276749);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(56, 161, 105, 0.3);
}

.meal-plan-modal-btn svg {
  color: #68d391;
}

/* Notification styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #38a169, #2f855a);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  animation: slideInRight 0.3s ease-out;
  font-weight: 500;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Compact modal sections */
.modal-content .meal-nutrition,
.modal-content .meal-steps,
.modal-content .meal-tags,
.modal-content .meal-types,
.modal-content .meal-ingredients {
  margin-bottom: 16px;
}

.modal-content .meal-nutrition h3,
.modal-content .meal-steps h3,
.modal-content .meal-tags h3,
.modal-content .meal-types h3,
.modal-content .meal-ingredients h3 {
  font-size: 1.1rem;
  margin-bottom: 8px;
  color: #2d3748;
}

.modal-content .meal-steps ol {
  margin: 0;
  padding-left: 20px;
}

.modal-content .meal-steps li {
  margin-bottom: 4px;
  font-size: 0.9rem;
  line-height: 1.4;
}

.family-container {
  max-width: 600px;
  margin: 0 auto;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.07);
  padding: 2rem 2.5rem;
}

.family-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.family-user {
  font-size: 1.1rem;
  color: #444;
  margin-bottom: 1rem;
}

.user-prefs-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.2rem 1rem;
  margin-bottom: 2rem;
  border: 1px solid #e3e3e3;
}

.user-prefs-section h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.15rem;
  color: #2b2b2b;
}

.family-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.family-form input {
  width: 100%;
  padding: 0.5rem 0.7rem;
  margin-bottom: 1rem;
  border: 1px solid #bbb;
  border-radius: 6px;
  font-size: 1rem;
  background: #f9f9f9;
}

.family-form button,
.add-member-btn {
  background: #2d8cff;
  color: #fff;
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  margin-top: 0.5rem;
  transition: background 0.2s;
}

.family-form button:hover,
.add-member-btn:hover {
  background: #1a6ed8;
}

.family-members-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.family-members-list li {
  background: #f5f7fa;
  border: 1px solid #e3e3e3;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.family-members-list strong {
  font-size: 1.08rem;
  color: #2d8cff;
}

.family-members-list em {
  color: #666;
  font-style: normal;
  font-weight: 500;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tag-btn {
  background: #f0f0f0;
  border: 1px solid #bbb;
  color: #333;
  border-radius: 20px;
  padding: 0.4rem 1rem;
  cursor: pointer;
  font-size: 0.98rem;
  transition: background 0.2s, color 0.2s, border 0.2s;
}

.tag-btn.selected {
  background: #2d8cff;
  color: #fff;
  border: 1.5px solid #2d8cff;
}

.preferences-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.preferences-modal {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
  overflow: hidden;
}

.preferences-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-bottom: 1px solid #dee2e6;
}

.preferences-modal .modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preferences-modal .modal-body {
  padding: 2rem;
}

.preferences-description {
  color: #6c757d;
  margin-bottom: 1.5rem;
  font-size: 1rem;
  line-height: 1.5;
}

.preferences-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.preferences-modal .form-group {
  margin-bottom: 0;
}

.preferences-modal .form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #495057;
  font-size: 1rem;
}

.preferences-modal select,
.preferences-modal input[type="number"] {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.preferences-modal select:focus,
.preferences-modal input[type="number"]:focus {
  outline: none;
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.form-help {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.85rem;
  color: #6c757d;
  font-style: italic;
}

.preferences-modal .modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid #f1f3f4;
}

.cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cancel-btn:hover {
  background: #5a6268;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.save-btn {
  background: var(--success-color);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-family: var(--font-family);
}

.save-btn:hover {
  background: var(--success-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

@media (max-width: 700px) {
  .family-container {
    padding: 1rem;
  }
}

/* Modern Family Profile Styles */
.family-container-modern {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  background: #ffffff;
}

.family-header-modern {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.family-title-section h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.family-subtitle {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

.current-user-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #f8fafc;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.user-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 1rem;
}

.user-role {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Card Styles */
.user-prefs-card, .add-member-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.card-header {
  margin-bottom: 1.5rem;
}

.card-header h2, .card-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.card-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

/* Modern Form Styles */
.modern-form {
  width: 100%;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label-text {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.form-input {
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: #ffffff;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
  color: #9ca3af;
}

.form-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-primary-modern {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
  font-family: var(--font-family);
}

.btn-primary-modern:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* ===== MODERN BUTTON SYSTEM ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-family: var(--font-family);
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-decoration: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Primary Button */
.btn-primary {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Secondary Button */
.btn-secondary {
  background: var(--secondary-color);
  color: white;
  border-color: var(--secondary-color);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--secondary-hover);
  border-color: var(--secondary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Success Button */
.btn-success {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.btn-success:hover:not(:disabled) {
  background: var(--success-hover);
  border-color: var(--success-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Warning Button */
.btn-warning {
  background: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
}

.btn-warning:hover:not(:disabled) {
  background: var(--warning-hover);
  border-color: var(--warning-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Error Button */
.btn-error {
  background: var(--error-color);
  color: white;
  border-color: var(--error-color);
}

.btn-error:hover:not(:disabled) {
  background: var(--error-hover);
  border-color: var(--error-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Outline Buttons */
.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border-color: var(--border-medium);
}

.btn-outline:hover:not(:disabled) {
  background: var(--gray-50);
  border-color: var(--gray-400);
  transform: translateY(-1px);
}

.btn-outline-primary {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover:not(:disabled) {
  background: var(--primary-light);
  color: var(--primary-dark);
  transform: translateY(-1px);
}

/* Ghost Buttons */
.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border-color: transparent;
  box-shadow: none;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--gray-100);
  color: var(--text-primary);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--font-size-base);
}

.btn-xl {
  padding: var(--space-5) var(--space-8);
  font-size: var(--font-size-lg);
}

/* Button with icon */
.btn-icon {
  padding: var(--space-3);
  width: auto;
  height: auto;
  min-width: 2.5rem;
  min-height: 2.5rem;
}

.btn-icon-sm {
  padding: var(--space-2);
  min-width: 2rem;
  min-height: 2rem;
}

.btn-icon-lg {
  padding: var(--space-4);
  min-width: 3rem;
  min-height: 3rem;
}

.success-message {
  color: #059669;
  font-weight: 500;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Family Members Section */
.family-members-section {
  margin-top: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.section-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

.add-member-btn-modern {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #ffffff;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-member-btn-modern:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.add-member-btn-modern span {
  font-weight: 600;
  font-size: 1rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  background: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 16px;
  margin-top: 1rem;
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.empty-state p {
  color: #6b7280;
  margin: 0 0 1.5rem 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Family Members Grid */
.family-members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.member-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.member-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.member-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1rem;
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.member-role {
  color: #6b7280;
  font-size: 0.875rem;
}

.remove-btn {
  position: absolute;
  top: 0;
  right: 0;
  background: #fee2e2;
  color: #dc2626;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn:hover {
  background: #fecaca;
  transform: scale(1.1);
}

/* Member Details */
.member-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.detail-value {
  color: #6b7280;
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

/* Responsive Design for Modern Family Profile */
@media (max-width: 768px) {
  .family-container-modern {
    padding: 1rem;
  }

  .family-header-modern {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .current-user-card {
    padding: 1rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .family-members-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .member-card {
    padding: 1rem;
  }

  .empty-state {
    padding: 2rem 1rem;
  }

  .add-member-card, .user-prefs-card {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .family-title-section h1 {
    font-size: 1.5rem;
  }

  .card-header h2, .card-header h3 {
    font-size: 1.25rem;
  }

  .form-input {
    padding: 0.625rem 0.75rem;
  }

  .btn-primary-modern {
    padding: 0.625rem 1.25rem;
    font-size: 0.8rem;
  }

  .member-header {
    margin-bottom: 1rem;
  }

  .member-avatar {
    width: 36px;
    height: 36px;
    font-size: 0.875rem;
  }

  .member-name {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-content {
    max-height: 95vh;
  }
  
  .modal-header {
    padding: 16px 16px 0 16px;
  }
  
  .modal-body {
    padding: 0 16px;
  }
  
  .modal-footer {
    padding: 16px;
  }
  
  .meal-image img {
    height: 200px;
  }
  
  .nutrition-grid {
    grid-template-columns: 1fr;
  }
  
  .meal-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .meal-steps li {
    padding-left: 50px;
  }
  
  .meal-steps li::before {
    width: 24px;
    height: 24px;
    font-size: 0.8rem;
  }
}

/* Calendar css */
.calendar-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 18px;
}

.calendar-controls button {
  background: #3182ce;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 6px 14px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.calendar-controls button:hover {
  background: #225ea8;
}

.month-label {
  font-weight: 600;
  font-size: 1.1rem;
  color: #2d3748;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 12px; /* more gap between days */
}

.calendar-day.locked {
  background: #e2e8f0;
  color: #a0aec0;
  cursor: not-allowed;
}

.calendar-day.empty {
  background: transparent;
  border: none;
  cursor: default;
}

.save-success-message {
  background: #c6f6d5;
  color: #22543d;
  border-radius: 6px;
  padding: 10px 18px;
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 1rem;
  justify-content: center;
}

.missed-plan-notification,
.missed-meal-alert {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
  border-radius: 6px;
  padding: 12px 18px;
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
}

.warning-icon,
.alert-icon {
  font-size: 1.3rem;
}

.dismiss-btn {
  background: transparent;
  border: none;
  color: #856404;
  margin-left: 10px;
  cursor: pointer;
  font-size: 1.1rem;
}

/* Enhanced Modal Overlay and Base Styles */
.meal-details-modal-overlay,
.meal-selector-modal-overlay,
.preferences-modal-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.meal-details-modal,
.meal-selector-modal,
.preferences-modal {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  padding: 0;
  min-width: 600px;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(30px);}
  to { opacity: 1; transform: translateY(0);}
}

/* Enhanced Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: linear-gradient(135deg, #20C5AF, #1ba896);
  color: white;
  border-radius: 16px 16px 0 0;
  margin-bottom: 0;
  flex-shrink: 0;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-header h2 svg {
  font-size: 1.3rem;
  opacity: 0.9;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: white;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Enhanced Modal Body */
.modal-body {
  padding: 32px;
  flex: 1;
  overflow-y: auto;
}

/* Enhanced Meal Type Section */
.meal-type-section {
  margin-bottom: 24px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #f1f5f9;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.meal-type-section:hover {
  border-color: #20C5AF;
  box-shadow: 0 4px 16px rgba(32, 197, 175, 0.1);
}

.meal-type-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f8f9fa;
}

.meal-type-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.meal-type-header h3::before {
  content: '';
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, #20C5AF, #1ba896);
  border-radius: 2px;
}
/* Enhanced Meal Time and Add Button */
.meal-type-header .meal-time {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.meal-type-header .meal-time svg {
  color: #20C5AF;
  font-size: 1rem;
}

.meal-type-header input[type="time"] {
  border: none;
  background: transparent;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  cursor: pointer;
}

.meal-type-header input[type="time"]:focus {
  outline: none;
}

.add-meal-btn {
  background: linear-gradient(135deg, #20C5AF, #1ba896);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(32, 197, 175, 0.2);
}

.add-meal-btn:hover {
  background: linear-gradient(135deg, #1ba896, #17a085);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(32, 197, 175, 0.3);
}

/* Enhanced Meal List */
.meal-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.meal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  min-height: 60px;
}

.meal-item:hover {
  background: #f1f3f4;
  border-color: #20C5AF;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(32, 197, 175, 0.1);
}

.meal-item:last-child {
  border-bottom: 1px solid #e9ecef;
}

/* Meal item content layout */
.meal-item .meal-name {
  font-weight: 600;
  color: #2d3748;
  flex: 1;
  font-size: 1rem;
  line-height: 1.4;
}

.meal-item .meal-calories {
  color: #20C5AF;
  font-size: 0.9rem;
  font-weight: 600;
  margin-right: 12px;
  white-space: nowrap;
}

/* Meal item actions container */
.meal-item-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Checkbox styling in meal items */
.meal-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #20C5AF;
}

/* New Meal Item Card Styles */
.meal-item-card {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 16px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.meal-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #20C5AF;
}

.meal-item-image {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.meal-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.meal-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7fafc;
  font-size: 24px;
}

.meal-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meal-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.meal-item-content .meal-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 1rem;
  line-height: 1.4;
  margin: 0;
}

.meal-item-content .meal-calories {
  color: #20C5AF;
  font-size: 0.9rem;
  font-weight: 600;
  white-space: nowrap;
}

.meal-description {
  color: #718096;
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0;
}

.meal-category {
  display: inline-block;
  background: #20C5AF;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.meal-item-bottom {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.view-meal-btn {
  background: #20C5AF;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s ease;
}

.view-meal-btn:hover {
  background: #1a9d8a;
}

.meal-item-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

/* Remove button styling */
.meal-item .remove-meal-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 0.8rem;
}

.meal-item .remove-meal-btn:hover {
  background: #c82333;
  transform: scale(1.05);
}
.remove-meal-btn, .add-btn {
  background: #e53e3e;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 2px 7px;
  font-size: 1rem;
  cursor: pointer;
  margin-left: 4px;
}
.remove-meal-btn:hover, .add-btn:hover {
  background: #9b2c2c;
}

.meal-type-calories {
  font-size: 0.98rem;
  color: #4a5568;
  margin-top: 4px;
}

.daily-calories {
  margin-top: 12px;
  font-size: 1.07rem;
  color: #22543d;
  font-weight: 600;
  text-align: right;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 8px;
}
.save-btn {
  background: #3182ce;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 7px 16px;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}
.save-btn:hover { background: #225ea8; }
.edit-btn {
  background: #f6ad55;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 7px 16px;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}
.edit-btn:hover { background: #c05621; }

.meal-selector-modal input[type="text"] {
  width: 100%;
  padding: 7px 10px;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  margin-bottom: 12px;
  font-size: 1rem;
}

.preferences-modal h2 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}
.preferences-modal label {
  display: block;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 4px;
}
.preferences-modal select,
.preferences-modal input[type="number"] {
  width: 100%;
  padding: 7px 10px;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  font-size: 1rem;
}
.preferences-modal .modal-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
.preferences-modal .save-btn {
  background: #38a169;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 7px 16px;
  font-size: 1rem;
  cursor: pointer;
}
.preferences-modal .save-btn:hover {
  background: #276749;
}

.error-container {
  background: #fed7d7;
  color: #742a2a;
  border-radius: 6px;
  padding: 16px 24px;
  margin: 24px 0;
  text-align: center;
}
.loading-container {
  background: #e2e8f0;
  color: #4a5568;
  border-radius: 6px;
  padding: 16px 24px;
  margin: 24px 0;
  text-align: center;
}

.calendar-day.past {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f1f1f1;
}

.close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  color: #000;
}

/* Admin View Toggle Button Styles */
.admin-view-toggle {
  background: linear-gradient(135deg, #20C5AF, #17a2b8);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  margin-right: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

/* Meal Tooltip Styles */
.meal-tooltip {
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  max-width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  animation: tooltipFadeIn 0.2s ease-out;
}

.meal-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-100%) scale(1);
  }
}

.tooltip-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 6px;
}

.tooltip-meals {
  margin-top: 8px;
}

.tooltip-meal-type {
  margin-bottom: 8px;
}

.tooltip-meal-type:last-child {
  margin-bottom: 0;
}

.tooltip-meal-type strong {
  display: block;
  color: #ffb347;
  font-size: 13px;
  margin-bottom: 4px;
}

.tooltip-meal-type ul {
  margin: 0;
  padding-left: 16px;
  list-style-type: disc;
}

.tooltip-meal-type li {
  margin-bottom: 2px;
  font-size: 12px;
  line-height: 1.4;
}

.tooltip-content p {
  margin: 0;
  text-align: center;
  color: #ccc;
  font-style: italic;
}

.admin-view-toggle:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.admin-view-toggle.user-mode {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.admin-view-toggle.admin-mode {
  background: linear-gradient(135deg, #20C5AF, #17a2b8);
}

.admin-view-toggle:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive design for admin toggle on smaller screens */
@media (max-width: 768px) {
  .admin-view-toggle {
    font-size: 12px;
    padding: 6px 12px;
    margin-right: 10px;
  }
}

/* Dietary Preferences Indicator Styles */
.dietary-info-section {
  background: #eafbe7;
  border: 1px solid #b7e4c7;
  border-radius: 12px;
  padding: 14px 20px;
  margin-bottom: 18px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
}

.dietary-info-header {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  font-size: 1.08rem;
  margin-bottom: 6px;
}

.dietary-info-header span {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.edit-preferences-btn {
  background: #ffb347;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 6px 16px;
  font-size: 0.97rem;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.18s;
}

.edit-preferences-btn:hover {
  background: #2bdfac;
  transform: translateY(-1px);
}

.dietary-info-content {
  display: flex;
  gap: 18px;
  flex-wrap: wrap;
}

.dietary-info-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.dietary-label {
  font-weight: 500;
  color: #388e3c;
}

.dietary-values {
  color: #222;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dietary-info-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .dietary-info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .dietary-label {
    min-width: auto;
  }
}

/* Age Display Styles */
.age-display {
  color: #70e4c4;
  font-weight: 600;
  font-size: 0.9em;
}

.member-age {
  font-size: 0.85rem;
  color: #70e4c4;
  font-weight: 500;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.member-age::before {
  content: "🎂";
  font-size: 0.8rem;
}

.form-hint {
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
  font-style: italic;
}

/* Meal Plan Favorites Styles */
.favorites-tabs {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  border-bottom: 2px solid #f0f0f0;
}

.tab-btn {
  background: none;
  border: none;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-btn:hover {
  color: #70e4c4;
}

.tab-btn.active {
  color: #70e4c4;
  border-bottom-color: #70e4c4;
}

.meal-plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.meal-plan-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.meal-plan-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.meal-plan-header {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.meal-plan-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #2c3e50;
}

.meal-plan-content {
  padding: 20px;
}

.meal-plan-meta {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.meta-item svg {
  color: #70e4c4;
  font-size: 16px;
}

.dietary-preference {
  background: #70e4c4;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: capitalize;
}

.view-meal-plan-btn {
  width: 100%;
  background: linear-gradient(135deg, #70e4c4, #2bdfac);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-meal-plan-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(112, 228, 196, 0.3);
}

/* Meal Plan Modal Styles */
.meal-plan-modal {
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
}

.plan-meta {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.meta-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.meta-row:last-child {
  margin-bottom: 0;
}

.meta-row svg {
  color: #70e4c4;
  font-size: 16px;
}

.dietary-preference-badge {
  background: #70e4c4;
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: capitalize;
}

.planned-meals h3 {
  margin-bottom: 16px;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 700;
}

/* Favorite Plan Summary Styles */
.favorite-plan-summary {
  padding: 20px 0;
}

.favorite-plan-summary h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 700;
  text-align: center;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  background: linear-gradient(135deg, #70e4c4, #2bdfac);
  color: white;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.calorie-icon {
  font-size: 24px;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.meal-types-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
}

.meal-types-section h4 {
  margin-bottom: 16px;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.meal-types-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.meal-type-badge {
  background: linear-gradient(135deg, #70e4c4, #2bdfac);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(112, 228, 196, 0.3);
}

.meal-type-icon {
  font-size: 16px;
}

.meal-type-name {
  text-transform: capitalize;
}

.plan-actions {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.action-note {
  margin-bottom: 20px;
}

.action-note p {
  color: #856404;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.recreate-plan-btn {
  background: linear-gradient(135deg, #70e4c4, #2bdfac);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.recreate-plan-btn:hover {
  background: linear-gradient(135deg, #2bdfac, #1dd1a1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(112, 228, 196, 0.4);
}

/* Enhanced meal plan details styles */
.meals-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.meal-entry {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #38a169;
}

.meal-entry strong {
  color: #2c3e50;
  font-size: 14px;
  display: block;
  margin-bottom: 6px;
}

.meal-items-inline {
  color: #4a5568;
  font-size: 13px;
  line-height: 1.4;
}

.meal-item-inline {
  display: inline;
}

.meal-calories-inline {
  color: #718096;
  font-size: 12px;
  font-weight: 500;
}

/* Grid view styles for meal plan details */
.meals-grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.day-card-grid {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.day-card-grid:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.day-header-grid {
  text-align: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #38a169;
}

.day-header-grid h4 {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 700;
}

.day-date {
  color: #718096;
  font-size: 14px;
  font-weight: 500;
}

.day-meals-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.meal-type-card {
  background: #f7fafc;
  border-radius: 8px;
  padding: 12px;
  border-left: 4px solid #38a169;
}

.meal-type-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.meal-time-icon {
  color: #38a169;
  font-size: 14px;
}

.meal-type-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.meal-items-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meal-item-card {
  background: #ffffff;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  transition: background-color 0.2s ease;
}

.meal-item-card:hover {
  background: #f0fff4;
}

/* .meal-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 13px;
  margin-bottom: 2px;
} */

.meal-calories {
  color: #38a169;
  font-size: 12px;
  font-weight: 500;
}

.meal-category {
  color: #718096;
  font-size: 11px;
  text-transform: capitalize;
}

.date-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.date-section:last-child {
  border-bottom: none;
}

.date-section h4 {
  margin-bottom: 12px;
  color: #70e4c4;
  font-size: 16px;
  font-weight: 600;
}

.meals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.meal-type-section h5 {
  margin-bottom: 8px;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
}

.meal-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.meal-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* .meal-name {
  font-size: 13px;
  color: #2c3e50;
  font-weight: 500;
} */

.meal-calories {
  font-size: 12px;
  color: #666;
  font-weight: 400;
}

/* Meal Plan Actions Button Styles */
.meal-plan-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.favorite-plan-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.favorite-plan-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
  background: linear-gradient(135deg, #c0392b, #a93226);
}

.favorite-plan-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.favorite-plan-btn.loading {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

/* Spinning animation for loading state */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.save-plan-btn {
  background: linear-gradient(135deg, #70e4c4, #2bdfac);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.save-plan-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(112, 228, 196, 0.3);
}

/* Responsive Design for Meal Plan Favorites */
@media (max-width: 768px) {
  .favorites-tabs {
    flex-direction: column;
    gap: 8px;
  }

  .tab-btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .meal-plans-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .meal-plan-actions {
    flex-direction: column;
    gap: 12px;
  }

  .meals-grid {
    grid-template-columns: 1fr;
  }

  .meal-plan-modal {
    max-height: 90vh;
  }

  .meals-grid-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  /* Responsive styles for favorite plan summary */
  .summary-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .stat-value {
    font-size: 20px;
  }

  .meal-types-list {
    justify-content: center;
  }

  .meal-type-badge {
    padding: 10px 16px;
    font-size: 13px;
  }

  .plan-actions {
    padding: 16px;
  }

  .recreate-plan-btn {
    padding: 12px 24px;
    font-size: 14px;
  }

  .day-card-grid {
    padding: 12px;
  }

  .meal-type-card {
    padding: 10px;
  }
}

/* No Meals Found Styling */
.no-meals-found {
  text-align: center;
  padding: 4rem 2rem;
  color: #64748b;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 2px dashed #cbd5e1;
  margin: 20px 0;
  transition: all 0.3s ease;
}

.no-meals-found:hover {
  border-color: #94a3b8;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.no-meals-found svg {
  font-size: 4rem;
  color: #94a3b8;
  margin-bottom: 1.5rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.no-meals-found p {
  margin: 0.5rem 0;
  font-size: 1rem;
}

.no-meals-found p:first-of-type {
  font-weight: 600;
  color: #475569;
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.no-meals-found p:last-of-type {
  color: #64748b;
  font-size: 1rem;
  margin-top: 0.5rem;
}

/* Meals Loading State */
.meals-loading {
  text-align: center;
  padding: 4rem 2rem;
  color: #64748b;
}

.meals-loading .circle-loader {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.meals-loading p {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Search Results Info */
.search-results-info {
  margin-bottom: 1.5rem;
  padding: 12px 16px;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.results-count {
  font-size: 0.95rem;
  color: #1e40af;
  font-weight: 500;
}

/* Additional Responsive Styles for New Meal Plan Layout */
@media (max-width: 768px) {
  .meal-plan-header-section {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    padding: 1rem;
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .preferences-btn, .generate-plan-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }

  .search-filter-section {
    flex-direction: row;
    gap: 1rem;
    padding: 16px;
  }

  .search-container {
    flex: 2;
  }

  .filter-container {
    flex: 1;
  }

  .search-container .meal-search,
  .dietary-filter {
    padding: 12px 16px;
    font-size: 1rem;
  }

  .meals-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }

  .meal-card-grid .meal-card-image {
    height: 140px;
  }

  .meal-card-grid .meal-card-title {
    font-size: 1rem;
  }

  .meal-card-grid .meal-card-description {
    font-size: 0.8rem;
  }

  .calendar-day {
    min-height: 120px;
    padding: 8px;
  }

  .day-number {
    font-size: 1.3rem;
  }

  .meal-indicators {
    gap: 2px;
  }

  .meal-indicator {
    font-size: 0.6rem;
    padding: 1px 4px;
  }

  .meal-count-badge {
    width: 14px;
    height: 14px;
    font-size: 0.55rem;
  }

  .day-summary {
    font-size: 0.65rem;
  }
}

@media (max-width: 480px) {
  .meals-grid {
    grid-template-columns: 1fr;
  }

  .meal-plan-header-section {
    padding: 0.75rem;
  }

  .header-title h1 {
    font-size: 1.5rem;
  }

  .header-subtitle {
    font-size: 0.9rem;
  }

  .calendar-day {
    min-height: 100px;
    padding: 6px;
  }

  .day-number {
    font-size: 1.1rem;
  }

  .meal-indicators {
    gap: 1px;
  }

  .meal-indicator {
    font-size: 0.55rem;
    padding: 1px 3px;
  }

  .day-summary {
    font-size: 0.6rem;
  }

  .preferences-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .preferences-modal .modal-header {
    padding: 1rem 1.5rem;
  }

  .preferences-modal .modal-header h2 {
    font-size: 1.3rem;
  }

  .preferences-modal .modal-body {
    padding: 1.5rem;
  }

  .preferences-modal .modal-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .cancel-btn, .save-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Required field styles */
.required {
  color: #dc3545;
  font-weight: 600;
  font-size: 0.875rem;
}

.form-help {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #6c757d;
  font-style: italic;
}

/* Family Dietary Preferences Section */
.family-preferences-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #e9ecef;
}

.family-preferences-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.family-preferences-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.25rem;
  font-weight: 600;
}

.manage-family-link {
  background-color: #20c5af;
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  transition: background-color 0.3s;
}

.manage-family-link:hover {
  background-color: #1ba896;
  color: white;
  text-decoration: none;
}

.family-members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.family-member-card {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-left: 4px solid #e0e0e0;
  transition: box-shadow 0.3s;
}

.family-member-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.family-member-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.family-member-name {
  margin: 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.family-member-age {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
}

.family-member-preferences {
  margin: 0;
  color: #6c757d;
  font-size: 0.875rem;
  line-height: 1.4;
}

/* Disliked Ingredients Styles */
.disliked-ingredients-input {
  margin-bottom: 1rem;
}

.disliked-ingredients-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.disliked-ingredient-item {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 20px;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.disliked-ingredient-item span {
  margin-right: 0.5rem;
}

.remove-ingredient {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 1.25rem;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.remove-ingredient:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

/* Age Legend Styles */
.age-legend {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e9ecef;
}

.age-legend h4 {
  margin: 0 0 0.75rem 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.age-legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.age-legend-item {
  display: flex;
  align-items: center;
  min-width: 150px;
}

.age-legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 0.5rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.age-legend-item span {
  font-size: 0.875rem;
  color: #495057;
}

/* AI Compatibility Styles */
.meal-compatibility-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-top: 8px;
  font-size: 12px;
  color: #6c757d;
}

.meal-compatibility-pending {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  background: #fff3cd;
  border-radius: 4px;
  margin-top: 6px;
  font-size: 11px;
  color: #856404;
  border-left: 2px solid #ffc107;
}

.meal-compatibility-loading .spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.meal-compatibility {
  margin-top: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #007bff;
}

.compatibility-header {
  margin-bottom: 6px;
}

.compatibility-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.compatibility-badge.excellent {
  background: #d4edda;
  color: #155724;
}

.compatibility-badge.good {
  background: #fff3cd;
  color: #856404;
}

.compatibility-badge.fair {
  background: #ffeaa7;
  color: #d68910;
}

.compatibility-badge.poor {
  background: #f8d7da;
  color: #721c24;
}

.compatibility-badge.unknown {
  background: #e2e3e5;
  color: #6c757d;
}

.compatibility-badge.pending {
  background: #fff3cd;
  color: #856404;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.family-compatibility {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.member-compatibility {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 6px;
  border-radius: 4px;
  font-size: 11px;
}

.member-compatibility.compatible {
  background: #d4edda;
  color: #155724;
}

.member-compatibility.incompatible {
  background: #f8d7da;
  color: #721c24;
}

.member-name {
  font-weight: 600;
  flex: 0 0 auto;
}

.compatibility-icon {
  flex: 0 0 auto;
  margin-left: 6px;
}

.compatibility-reasons {
  flex: 1;
  margin-left: 8px;
  margin-right: 8px;
}

.reason-text {
  font-size: 10px;
  font-style: italic;
  opacity: 0.8;
}

.concern-text {
  font-size: 10px;
  font-weight: 500;
  flex: 1;
  margin-left: 4px;
}

/* Responsive adjustments for meal cards */
@media (max-width: 768px) {
  .meal-compatibility {
    padding: 6px;
    margin-top: 6px;
  }

  .member-compatibility {
    padding: 3px 4px;
    font-size: 10px;
  }

  .compatibility-badge {
    font-size: 9px;
    padding: 1px 4px;
  }

  .reason-text {
    font-size: 9px;
  }
}

/* Validation Modal Styles */
.validation-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.validation-modal {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.validation-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-bottom: 1px solid #dee2e6;
  flex-shrink: 0;
}

.validation-modal .modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.validation-modal .close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.validation-modal .close-btn:hover {
  color: #ff6b6b;
  background-color: #f7fafc;
}

.validation-modal .modal-body {
  padding: 2rem;
  flex: 1;
  overflow-y: auto;
}

.validation-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.validation-loading .spinner {
  font-size: 2rem;
  color: #3182ce;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.validation-loading p {
  color: #6c757d;
}

/* Save button spinner */
.save-btn .spinner {
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

.save-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.validation-results {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.validation-summary {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid #3182ce;
}

.validation-summary h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.validation-summary p {
  margin: 0;
  color: #495057;
  font-size: 1rem;
}

.validation-members h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.validation-member-card {
  border-radius: 12px;
  padding: 1rem 1.5rem;
  border: 2px solid;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  box-sizing: border-box;
}

.validation-member-card.good {
  background: linear-gradient(135deg, #f0fff4, #e6fffa);
  border-color: #48bb78;
}

.validation-member-card.warning {
  background: linear-gradient(135deg, #fffaf0, #fef5e7);
  border-color: #ed8936;
}

.validation-member-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.member-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  gap: 0.5rem;
}

.member-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: #2d3748;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  flex: 1;
  min-width: 0;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  white-space: nowrap;
  flex-shrink: 0;
}

.status-badge.good {
  background: #48bb78;
  color: white;
}

.status-badge.warning {
  background: #ed8936;
  color: white;
}

.member-concern {
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.4;
  color: #4a5568;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  white-space: pre-wrap;
}

.validation-modal .modal-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e5e5e5;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  flex-shrink: 0;
  background: #f8f9fa;
}

.validation-modal .btn-primary,
.validation-modal .btn-secondary {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.validation-modal .btn-primary {
  background: linear-gradient(135deg, #3182ce, #2c5aa0);
  color: white;
}

.validation-modal .btn-primary:hover {
  background: linear-gradient(135deg, #2c5aa0, #2a4d8d);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(49, 130, 206, 0.3);
}

.validation-modal .btn-secondary {
  background: white;
  color: #4a5568;
  border: 2px solid #e2e8f0;
}

.validation-modal .btn-secondary:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design for Validation Modal */
@media (max-width: 768px) {
  .validation-modal {
    width: 95%;
    max-height: 90vh;
    margin: 10px;
  }

  .validation-modal .modal-header {
    padding: 1rem 1.5rem;
  }

  .validation-modal .modal-header h2 {
    font-size: 1.3rem;
  }

  .validation-modal .modal-body {
    padding: 1.5rem;
  }

  .validation-modal .modal-footer {
    padding: 1rem 1.5rem;
    flex-direction: column;
    gap: 0.75rem;
  }

  .validation-modal .btn-primary,
  .validation-modal .btn-secondary {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .validation-modal {
    width: 100%;
    height: 100%;
    max-height: 100vh;
    border-radius: 0;
  }

  .validation-summary,
  .validation-member-card {
    padding: 1rem;
  }

  .member-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* ===== USER-FRIENDLY ENHANCEMENTS ===== */

/* Modern Form Inputs */
.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
  transform: translateY(-1px);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-light);
}

/* Form Groups */
.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  font-family: var(--font-family);
}

.form-label.required::after {
  content: ' *';
  color: var(--error-color);
}

/* User-Friendly Notifications */
.notification {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-family: var(--font-family);
  box-shadow: var(--shadow-sm);
}

.notification.success {
  background: var(--success-light);
  color: var(--success-color);
  border-left: 4px solid var(--success-color);
}

.notification.warning {
  background: var(--warning-light);
  color: var(--warning-color);
  border-left: 4px solid var(--warning-color);
}

.notification.error {
  background: var(--error-light);
  color: var(--error-color);
  border-left: 4px solid var(--error-color);
}

.notification.info {
  background: var(--secondary-light);
  color: var(--secondary-color);
  border-left: 4px solid var(--secondary-color);
}

/* ===== CONSISTENT CIRCLE LOADERS ===== */

/* Main circle loader - default size */
.circle-loader {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: circleSpinner 1s linear infinite;
  display: inline-block;
}

/* Circle loader sizes */
.circle-loader.small {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.circle-loader.medium {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

.circle-loader.large {
  width: 48px;
  height: 48px;
  border-width: 4px;
}

.circle-loader.extra-large {
  width: 64px;
  height: 64px;
  border-width: 5px;
}

/* Circle loader colors */
.circle-loader.primary {
  border-top-color: var(--primary-color);
}

.circle-loader.secondary {
  border-top-color: var(--secondary-color);
}

.circle-loader.success {
  border-top-color: var(--success-color);
}

.circle-loader.warning {
  border-top-color: var(--warning-color);
}

.circle-loader.error {
  border-top-color: var(--error-color);
}

.circle-loader.white {
  border-color: rgba(255, 255, 255, 0.3);
  border-top-color: white;
}

/* Loading states with circle loader */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  color: var(--text-secondary);
  font-family: var(--font-family);
}

.loading-state .circle-loader {
  margin-bottom: var(--space-4);
}

/* Inline loading with circle loader */
.loading-inline {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--text-secondary);
  font-family: var(--font-family);
}

.loading-inline .circle-loader {
  margin: 0;
}

/* Button loading state */
.btn-loading {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.btn-loading .circle-loader {
  margin-right: var(--space-2);
}

/* Overlay loading */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-overlay .loading-content {
  background: var(--bg-primary);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  text-align: center;
  min-width: 200px;
}

.loading-overlay .circle-loader {
  margin-bottom: var(--space-4);
}

.loading-overlay .loading-text {
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: 500;
  margin: 0;
  font-family: var(--font-family);
}

/* Animation for circle loader */
@keyframes circleSpinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Legacy support - redirect old spinner classes to circle loader */
.spinner,
.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: circleSpinner 1s linear infinite;
  display: inline-block;
}

/* Legacy spin animation redirect */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: var(--space-12);
  color: var(--text-secondary);
  font-family: var(--font-family);
}

.empty-state-icon {
  font-size: var(--font-size-4xl);
  color: var(--text-light);
  margin-bottom: var(--space-4);
}

.empty-state h3 {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.empty-state p {
  font-size: var(--font-size-base);
  margin-bottom: var(--space-6);
}

/* Responsive Helpers */
.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }

  .desktop-only {
    display: none;
  }
}

/* ===== SERVING SIZE CONTROLS ===== */
.serving-size-control {
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.serving-size-control h3 {
  margin: 0 0 var(--space-3) 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  font-family: var(--font-family);
}

.serving-size-selector {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.serving-btn {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  border: 2px solid var(--primary-color);
  background: var(--bg-primary);
  color: var(--primary-color);
  font-size: var(--font-size-xl);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-family);
}

.serving-btn:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
  transform: scale(1.05);
}

.serving-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.serving-size-display {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  min-width: 120px;
  text-align: center;
  font-family: var(--font-family);
}

/* Per meal label styling */
.per-meal-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 400;
  font-style: italic;
}

/* Serving note styling */
.serving-note {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 400;
  font-style: italic;
}

/* Enhanced nutrition grid for serving size */
.nutrition-grid .nutrition-item {
  transition: all 0.2s ease;
}

.nutrition-grid .nutrition-item:hover {
  background: var(--primary-light);
  border-radius: var(--radius-md);
  padding: var(--space-2);
  margin: calc(-1 * var(--space-2));
}

/* Responsive serving size controls */
@media (max-width: 768px) {
  .serving-size-selector {
    gap: var(--space-3);
  }

  .serving-btn {
    width: 36px;
    height: 36px;
    font-size: var(--font-size-lg);
  }

  .serving-size-display {
    font-size: var(--font-size-base);
    min-width: 100px;
  }
}

/* ===== MEAL CARD INGREDIENTS ===== */
.meal-card-ingredients {
  margin-top: var(--space-3);
  padding: var(--space-3) 0;
  border-top: 1px solid var(--border-light);
}

.meal-card-ingredients h4 {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
  font-family: var(--font-family);
}

.ingredients-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.ingredient-item {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-family: var(--font-family);
  line-height: 1.3;
}

.more-ingredients {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  font-style: italic;
  font-family: var(--font-family);
}

/* ===== MEAL CARD SERVING CONTROLS (COUNTER STYLE) ===== */
.meal-card-serving-control {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: var(--space-3);
  padding: var(--space-2) 0;
}

.serving-controls {
  display: flex;
  align-items: center;
  background: var(--text-primary);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.serving-btn-card {
  width: 32px;
  height: 28px;
  border: none;
  background: var(--text-primary);
  color: white;
  font-size: var(--font-size-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-family);
}

.serving-btn-card:hover:not(:disabled) {
  background: var(--text-secondary);
}

.serving-btn-card:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.serving-count-card {
  background: white;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: 600;
  padding: 0 var(--space-3);
  min-width: 40px;
  text-align: center;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-family);
}

/* ===== COMPACT INGREDIENTS FOR MEAL CARDS ===== */
.meal-card-ingredients-compact {
  margin-top: var(--space-2);
  padding: var(--space-2) 0;
}

.ingredients-header {
  margin-bottom: var(--space-1);
}

.ingredients-title {
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--text-primary);
  font-family: var(--font-family);
}

.ingredients-preview {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  line-height: 1.3;
}

.ingredient-item {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.view-more-ingredients {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: var(--font-size-xs);
  font-weight: 600;
  cursor: pointer;
  padding: 0;
  text-decoration: underline;
  font-family: var(--font-family);
}

.view-more-ingredients:hover {
  color: var(--primary-hover);
}

/* ===== COMPACT SERVING SIZE CONTROLS ===== */
.serving-size-controls-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: var(--space-2);
  padding: var(--space-1) var(--space-2);
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
}

.serving-label-compact {
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--text-primary);
  font-family: var(--font-family);
}

.serving-controls-compact {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.serving-btn-compact {
  width: 24px;
  height: 24px;
  border: 1px solid var(--primary-color);
  background: var(--bg-primary);
  color: var(--primary-color);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-family);
  transition: all 0.2s ease;
}

.serving-btn-compact:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
  transform: scale(1.05);
}

.serving-btn-compact:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.serving-count-compact {
  min-width: 20px;
  text-align: center;
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--text-primary);
  font-family: var(--font-family);
}

.serving-note {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: 400;
  font-style: italic;
}

/* ===== LEGACY SERVING SIZE CONTROLS (for modal) ===== */
.serving-size-controls-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: var(--space-3);
  padding: var(--space-2);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.serving-label {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
  font-family: var(--font-family);
}

.serving-controls {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

/* Responsive meal card controls */
@media (max-width: 768px) {
  .meal-card-ingredients {
    margin-top: var(--space-2);
    padding: var(--space-2) 0;
  }

  .serving-btn-card {
    width: 28px;
    height: 24px;
    font-size: var(--font-size-base);
  }

  .serving-count-card {
    height: 24px;
    min-width: 32px;
    padding: 0 var(--space-2);
    font-size: var(--font-size-sm);
  }

  .ingredient-item {
    font-size: 0.7rem;
  }

  .serving-size-controls-card {
    margin-top: var(--space-2);
    padding: var(--space-2);
  }

  .serving-label {
    font-size: var(--font-size-xs);
  }

  .meal-card-ingredients-compact {
    margin-top: var(--space-1);
    padding: var(--space-1) 0;
  }

  .ingredients-preview {
    font-size: 0.65rem;
  }

  .serving-size-controls-compact {
    margin-top: var(--space-1);
    padding: var(--space-1);
  }
}

/* ===== MEAL PLAN SERVING SIZE CONTROLS ===== */
.meal-plan-serving-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: var(--space-2);
  padding: var(--space-1) var(--space-2);
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
}

.serving-label-small {
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--text-secondary);
  font-family: var(--font-family);
}

.serving-controls-small {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.serving-btn-small {
  width: 24px;
  height: 24px;
  border: 1px solid var(--border-medium);
  background: var(--bg-primary);
  color: var(--text-primary);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-family: var(--font-family);
}

.serving-btn-small:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

.serving-btn-small:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--bg-disabled);
  color: var(--text-disabled);
}

.serving-count-small {
  min-width: 20px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--text-primary);
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  padding: 0 var(--space-1);
  font-family: var(--font-family);
}

/* Responsive adjustments for meal plan serving controls */
@media (max-width: 768px) {
  .meal-plan-serving-controls {
    margin-top: var(--space-1);
    padding: var(--space-1);
  }

  .serving-label-small {
    font-size: 0.65rem;
  }

  .serving-btn-small {
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
  }

  .serving-count-small {
    min-width: 18px;
    height: 20px;
    font-size: 0.65rem;
    padding: 0 var(--space-1);
  }

  .serving-btn-compact {
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
  }

  .serving-count-compact {
    font-size: 0.7rem;
    min-width: 16px;
  }
}

/* ===== MEAL MODAL IMAGE FIX ===== */
.meal-image {
  width: 100%;
  max-width: 500px;
  margin-bottom: var(--space-6);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.meal-image img {
  width: 100%;
  height: auto;
  min-height: 250px;
  max-height: 400px;
  object-fit: cover;
  display: block;
}

/* Responsive meal modal image */
@media (max-width: 768px) {
  .meal-image {
    max-width: 100%;
    margin-bottom: var(--space-4);
  }

  .meal-image img {
    min-height: 200px;
    max-height: 300px;
  }
}

/* ===== SAVE MEAL PLAN MODAL STYLES ===== */
.save-meal-plan-modal {
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
}

.save-meal-plan-modal .modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--primary-light);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.save-meal-plan-modal .modal-header h2 {
  color: var(--primary-dark);
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin: 0;
}

.save-meal-plan-modal .modal-header .close-btn {
  background: rgba(255, 255, 255, 0.9) !important;
  border: none !important;
  font-size: 20px !important;
  cursor: pointer !important;
  color: var(--primary-dark) !important;
  border-radius: 50% !important;
  transition: background-color 0.2s ease, color 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 36px !important;
  height: 36px !important;
  padding: 0 !important;
  opacity: 1 !important;
  visibility: visible !important;
  position: relative !important;
  z-index: 10 !important;
  transform: none !important;
}

.save-meal-plan-modal .modal-header .close-btn:hover {
  background: rgba(255, 255, 255, 1) !important;
  color: #ff6b6b !important;
  transform: none !important;
}

.save-meal-plan-modal .modal-header .close-btn svg {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* Ensure all modal close buttons remain visible */
.modal-header .close-btn {
  opacity: 1 !important;
  visibility: visible !important;
}

.modal-header .close-btn svg,
.modal-header .close-btn i {
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-block !important;
}

.save-meal-plan-modal .modal-body {
  padding: var(--space-6);
}

.save-plan-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.save-plan-form .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.save-plan-form .form-group label {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.save-plan-form .form-input {
  padding: var(--space-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  transition: all 0.2s ease;
  background: var(--bg-primary);
}

.save-plan-form .form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.save-plan-form .form-hint {
  color: var(--text-secondary);
  font-size: var(--text-xs);
  margin-top: var(--space-1);
}

.date-range-section {
  background: var(--gray-50);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

.date-range-section h3 {
  margin: 0 0 var(--space-4) 0;
  color: var(--text-primary);
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
}

.date-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.meal-plan-preview {
  background: var(--primary-light);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  border: 1px solid var(--primary-color);
}

.meal-plan-preview h3 {
  margin: 0 0 var(--space-4) 0;
  color: var(--primary-dark);
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
}

.preview-summary {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.preview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-3);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2);
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--gray-200);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.stat-value {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: var(--font-semibold);
}

.preview-details {
  margin-top: var(--space-4);
}

.preview-details h4 {
  margin: 0 0 var(--space-3) 0;
  color: var(--primary-dark);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
}

.meals-by-date {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  max-height: 200px;
  overflow-y: auto;
}

.date-meal-summary {
  background: var(--bg-primary);
  padding: var(--space-3);
  border-radius: var(--radius-sm);
  border: 1px solid var(--gray-200);
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.date-label {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.meal-count {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  background: var(--gray-100);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
}

.meal-types {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.meal-type {
  font-size: var(--text-xs);
  background: var(--primary-light);
  color: var(--primary-dark);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  border: 1px solid var(--primary-color);
}

.no-meals-warning {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background: var(--warning-light);
  border: 1px solid var(--warning-color);
  border-radius: var(--radius-md);
  color: var(--warning-hover);
}

.no-meals-warning p {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.save-meal-plan-modal .modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-3);
  background: var(--gray-50);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.save-meal-plan-modal .action-buttons {
  display: flex;
  gap: var(--space-3);
}

.save-meal-plan-modal .btn-cancel {
  padding: var(--space-3) var(--space-6);
  background: var(--bg-primary);
  color: var(--text-secondary);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-meal-plan-modal .btn-cancel:hover:not(:disabled) {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.save-meal-plan-modal .btn-save {
  padding: var(--space-3) var(--space-6);
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.save-meal-plan-modal .btn-save:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.save-meal-plan-modal .btn-save:disabled {
  background: var(--gray-300);
  color: var(--gray-500);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.save-meal-plan-modal .btn-favorites {
  padding: var(--space-3) var(--space-6);
  background: var(--secondary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.save-meal-plan-modal .btn-favorites:hover:not(:disabled) {
  background: var(--secondary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.save-meal-plan-modal .btn-favorites:disabled {
  background: var(--gray-300);
  color: var(--gray-500);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .save-meal-plan-modal {
    width: 95%;
    margin: var(--space-4);
  }

  .date-inputs {
    grid-template-columns: 1fr;
  }

  .preview-stats {
    grid-template-columns: 1fr;
  }

  .save-meal-plan-modal .modal-footer {
    flex-direction: column;
    gap: var(--space-4);
  }

  .save-meal-plan-modal .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .save-meal-plan-modal .btn-cancel,
  .save-meal-plan-modal .btn-save,
  .save-meal-plan-modal .btn-favorites {
    width: 100%;
    justify-content: center;
  }
}
