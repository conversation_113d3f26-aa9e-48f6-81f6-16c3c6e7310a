require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User');

async function createTestUser() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if test user already exists
    const existingUser = await User.findOne({ email: '<EMAIL>' });
    if (existingUser) {
      console.log('Test user already exists');
      // Update the user to ensure they have terms accepted
      existingUser.isEmailVerified = true;
      existingUser.termsAccepted = true;
      existingUser.termsAcceptedAt = new Date();
      existingUser.termsVersion = '1.0';
      await existingUser.save();
      console.log('Updated existing test user');
    } else {
      // Create new test user
      const testUser = new User({
        username: 'testapi',
        email: '<EMAIL>',
        password: 'testpassword123',
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: true,
        termsAccepted: true,
        termsAcceptedAt: new Date(),
        termsVersion: '1.0'
      });

      await testUser.save();
      console.log('Test user created successfully');
    }

    // Close connection
    await mongoose.connection.close();
    console.log('Test user ready: <EMAIL> / testpassword123');
  } catch (error) {
    console.error('Error:', error);
  }
}

createTestUser();
