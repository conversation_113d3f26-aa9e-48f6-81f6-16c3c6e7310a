require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User');
const jwt = require('jsonwebtoken');
const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

async function testAPIDirectly() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get a user with test data
    const user = await User.findOne({ email: '<EMAIL>' });
    if (!user) {
      console.log('Test user not found');
      return;
    }

    console.log(`Found user: ${user.email}`);
    console.log(`Recently added meals: ${user.recentlyAddedToMealPlans?.length || 0}`);

    // Create a JWT token for this user
    const token = jwt.sign(
      { id: user._id, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );

    console.log('Generated JWT token for user');

    // Test the API endpoint
    console.log('Testing API endpoint...');
    const response = await axios.get(`${API_BASE_URL}/users/recently-added-to-meal-plans`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    console.log('API Response:', JSON.stringify(response.data, null, 2));

    const meals = response.data.recentlyAddedToMealPlans || [];
    console.log(`\n✅ API returned ${meals.length} meals:`);
    meals.forEach((meal, index) => {
      console.log(`${index + 1}. ${meal.name} - ${meal.calories} cal - ${meal.addedToMealType} on ${meal.addedToDate}`);
      console.log(`   Image: ${meal.image}`);
      console.log(`   Description: ${meal.description}`);
    });

    // Close connection
    await mongoose.connection.close();
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

testAPIDirectly();
