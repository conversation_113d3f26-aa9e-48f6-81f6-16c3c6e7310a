const mongoose = require('mongoose');
const Activity = require('./models/Activity');
const User = require('./models/User');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/meal-planner', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function createTestActivities() {
  try {
    console.log('Creating test activities...');

    // Find a test user or create one
    let testUser = await User.findOne({ email: '<EMAIL>' });
    if (!testUser) {
      testUser = new User({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'hashedpassword',
        isEmailVerified: true
      });
      await testUser.save();
      console.log('Created test user');
    }

    // Create sample activities
    const sampleActivities = [
      {
        user: testUser._id,
        action: 'login',
        details: { userAgent: 'Mozilla/5.0 Test Browser' },
        ipAddress: '***********'
      },
      {
        user: testUser._id,
        action: 'create_meal_plan',
        details: { date: '2025-01-15', mealType: 'breakfast' },
        ipAddress: '***********'
      },
      {
        user: testUser._id,
        action: 'create_meal',
        details: { mealName: 'Chicken Adobo' },
        ipAddress: '***********'
      },
      {
        user: testUser._id,
        action: 'made_admin',
        details: { targetUser: '<EMAIL>' },
        ipAddress: '***********'
      },
      {
        user: testUser._id,
        action: 'submitted_feedback',
        details: { feedbackSubject: 'App Performance Issue' },
        ipAddress: '***********'
      }
    ];

    // Clear existing test activities
    await Activity.deleteMany({ user: testUser._id });

    // Insert new activities
    for (const activityData of sampleActivities) {
      const activity = new Activity(activityData);
      await activity.save();
      console.log(`Created activity: ${activity.action}`);
    }

    console.log('Test activities created successfully!');

    // Test the recent activity endpoint format
    const activities = await Activity.find({ user: testUser._id })
      .populate('user', 'username email')
      .sort({ createdAt: -1 })
      .limit(10);

    console.log('\nFormatted activities:');
    activities.forEach(activity => {
      console.log({
        type: activity.action,
        username: activity.user.username,
        description: `Action: ${activity.action}`,
        timestamp: activity.createdAt,
        details: activity.details
      });
    });

  } catch (error) {
    console.error('Error creating test activities:', error);
  } finally {
    mongoose.connection.close();
  }
}

createTestActivities();
