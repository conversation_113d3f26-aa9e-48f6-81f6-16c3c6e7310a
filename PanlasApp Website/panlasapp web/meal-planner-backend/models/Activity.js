const mongoose = require('mongoose');

const ActivitySchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  action: {
    type: String,
    required: true,
    enum: [
      'login',
      'logout',
      'create_meal_plan',
      'update_meal_plan',
      'delete_meal_plan',
      'create_meal',
      'update_meal',
      'delete_meal',
      'update_profile',
      'made_admin',
      'made_sub_admin',
      'made_user',
      'disabled_account',
      'enabled_account',
      'deleted_feedback',
      'submitted_feedback',
      'updated_feedback_status',
      'password_reset',
      'email_verification',
      'signup'
    ]
  },
  details: {
    type: Object,
    default: {}
  },
  ipAddress: {
    type: String
  }
}, { timestamps: true });

module.exports = mongoose.model('Activity', ActivitySchema);
