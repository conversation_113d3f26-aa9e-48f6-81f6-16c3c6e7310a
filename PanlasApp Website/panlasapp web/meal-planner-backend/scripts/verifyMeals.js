const mongoose = require('mongoose');
const Meal = require('../models/Meal');
require('dotenv').config();

async function verifyMeals() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
    
    // Get total count
    const totalCount = await Meal.countDocuments();
    console.log(`📊 Total meals in database: ${totalCount}`);
    
    // Get a few sample meals to verify the data structure
    const meals = await Meal.find().limit(3);
    
    console.log('\n📋 Sample meal data verification:');
    meals.forEach((meal, index) => {
      console.log(`\n--- Meal ${index + 1}: ${meal.name} ---`);
      console.log(`Category: ${meal.category}`);
      console.log(`MealType: ${meal.mealType}`);
      console.log(`Calories: ${meal.calories}`);
      console.log(`Image: ${meal.image}`);
      console.log(`Allergens: ${meal.allergens}`);
      console.log(`DietType - Vegetarian: ${meal.dietType.isVegetarian}, Vegan: ${meal.dietType.isVegan}, Halal: ${meal.dietType.isHalal}`);
      console.log(`Ingredients count: ${meal.ingredients.length}`);
      console.log(`Instructions count: ${meal.instructions.length}`);
    });
    
    // Check for meals with images
    const mealsWithImages = await Meal.countDocuments({ image: { $exists: true, $ne: '' } });
    console.log(`\n🖼️  Meals with images: ${mealsWithImages}/${totalCount}`);
    
    // Check dietary types distribution
    const vegetarianCount = await Meal.countDocuments({ 'dietType.isVegetarian': true });
    const veganCount = await Meal.countDocuments({ 'dietType.isVegan': true });
    const halalCount = await Meal.countDocuments({ 'dietType.isHalal': true });

    console.log(`\n🥗 Dietary distribution:`);
    console.log(`- Vegetarian: ${vegetarianCount}`);
    console.log(`- Vegan: ${veganCount}`);
    console.log(`- Halal: ${halalCount}`);

    // Check meal category distribution for meal plan generation
    console.log(`\n🍽️  Meal category distribution (by category field):`);
    const breakfastCount = await Meal.countDocuments({ category: { $in: ['breakfast'] } });
    const lunchCount = await Meal.countDocuments({ category: { $in: ['lunch'] } });
    const dinnerCount = await Meal.countDocuments({ category: { $in: ['dinner'] } });

    console.log(`- Breakfast: ${breakfastCount}`);
    console.log(`- Lunch: ${lunchCount}`);
    console.log(`- Dinner: ${dinnerCount}`);

    // Check meal type distribution (this is what should be used for meal plan generation)
    console.log(`\n🍽️  Meal type distribution (by mealType field):`);
    const breakfastMealTypeCount = await Meal.countDocuments({ mealType: { $in: ['breakfast'] } });
    const lunchMealTypeCount = await Meal.countDocuments({ mealType: { $in: ['lunch'] } });
    const dinnerMealTypeCount = await Meal.countDocuments({ mealType: { $in: ['dinner'] } });

    console.log(`- Breakfast: ${breakfastMealTypeCount}`);
    console.log(`- Lunch: ${lunchMealTypeCount}`);
    console.log(`- Dinner: ${dinnerMealTypeCount}`);

    // Check all unique categories and meal types to understand the data structure
    const allCategories = await Meal.distinct('category');
    const allMealTypes = await Meal.distinct('mealType');
    console.log(`\n📋 All unique categories found:`, allCategories);
    console.log(`\n📋 All unique meal types found:`, allMealTypes);

    // Check meals that contain breakfast, lunch, or dinner in their mealType array
    const breakfastMeals = await Meal.find({ mealType: { $in: ['breakfast'] } }).select('name mealType').limit(5);
    const lunchMeals = await Meal.find({ mealType: { $in: ['lunch'] } }).select('name mealType').limit(5);
    const dinnerMeals = await Meal.find({ mealType: { $in: ['dinner'] } }).select('name mealType').limit(5);

    console.log(`\n🌅 Sample breakfast meals:`);
    breakfastMeals.forEach(meal => console.log(`- ${meal.name} (${meal.mealType.join(', ')})`));

    console.log(`\n🌞 Sample lunch meals:`);
    lunchMeals.forEach(meal => console.log(`- ${meal.name} (${meal.mealType.join(', ')})`));

    console.log(`\n🌙 Sample dinner meals:`);
    dinnerMeals.forEach(meal => console.log(`- ${meal.name} (${meal.mealType.join(', ')})`));

    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    
  } catch (error) {
    console.error('❌ Error verifying meals:', error);
  }
}

verifyMeals();
