const User = require('../models/User');
const MealPlan = require('../models/MealPlan');
const Admin = require('../models/Admin');
const bcrypt = require('bcryptjs');
const ActivityService = require('../services/activityService');

// Get all users (admin only)
exports.getAllUsers = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const users = await User.find()
      .select('-password')
      .sort({ createdAt: -1 });

    // Format dates for easier reading
    const formattedUsers = users.map(user => {
      const userData = user.toObject();
      userData.formattedCreatedAt = new Date(user.createdAt).toLocaleString();
      userData.accountAge = Math.floor((Date.now() - new Date(user.createdAt)) / (1000 * 60 * 60 * 24)); // in days
      // Ensure isActive field is properly set (default to true for any edge cases)
      userData.isActive = userData.isActive === false ? false : true;
      return userData;
    });

    res.json(formattedUsers);
  } catch (error) {
    console.error('Admin error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get user signup statistics
exports.getUserSignupStats = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    // Get total verified users count only
    const totalUsers = await User.countDocuments({ isEmailVerified: true });

    // Get verified users who signed up today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todaySignups = await User.countDocuments({
      createdAt: { $gte: today },
      isEmailVerified: true
    });

    // Get verified users who signed up this week
    const startOfWeek = new Date();
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
    startOfWeek.setHours(0, 0, 0, 0);
    const weeklySignups = await User.countDocuments({
      createdAt: { $gte: startOfWeek },
      isEmailVerified: true
    });

    // Get verified users who signed up this month
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);
    const monthlySignups = await User.countDocuments({
      createdAt: { $gte: startOfMonth },
      isEmailVerified: true
    });

    // Get monthly signups for verified users for the past 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyStats = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo },
          isEmailVerified: true
        }
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1 }
      }
    ]);

    // Format monthly stats for easier consumption
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const formattedMonthlyStats = monthlyStats.map(stat => ({
      year: stat._id.year,
      month: stat._id.month,
      monthName: months[stat._id.month - 1],
      count: stat.count,
      label: `${months[stat._id.month - 1]} ${stat._id.year}`
    }));

    res.json({
      totalUsers,
      todaySignups,
      weeklySignups,
      monthlySignups,
      monthlyStats: formattedMonthlyStats
    });
  } catch (error) {
    console.error('Admin stats error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get system overview
exports.getSystemOverview = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    // Get total verified users only
    const totalUsers = await User.countDocuments({ isEmailVerified: true });

    // Get meal plans only for verified users
    const verifiedUserIds = await User.find({ isEmailVerified: true }).select('_id');
    const verifiedUserIdArray = verifiedUserIds.map(user => user._id);

    const totalMealPlans = await MealPlan.countDocuments({
      user: { $in: verifiedUserIdArray }
    });

    // Get average meal plans per verified user
    const avgMealPlansPerUser = totalUsers > 0 ? totalMealPlans / totalUsers : 0;

    // Get latest 5 verified signups
    const latestSignups = await User.find({ isEmailVerified: true })
      .select('username email createdAt')
      .sort({ createdAt: -1 })
      .limit(5);

    // Format dates for easier reading
    const formattedLatestSignups = latestSignups.map(user => ({
      id: user._id,
      username: user.username,
      email: user.email,
      createdAt: new Date(user.createdAt).toLocaleString()
    }));

    res.json({
      totalUsers,
      totalMealPlans,
      avgMealPlansPerUser: avgMealPlansPerUser.toFixed(2),
      latestSignups: formattedLatestSignups
    });
  } catch (error) {
    console.error('System overview error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

exports.getUserRegistrationReport = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    // Get date range from query params
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({ message: 'Start date and end date are required' });
    }

    // Create date objects
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Add one day to end date to include the end date in the range
    end.setDate(end.getDate() + 1);

    // Validate dates
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({ message: 'Invalid date format' });
    }

    // Find users registered within the date range
    const users = await User.find({
      createdAt: { $gte: start, $lt: end }
    }).select('username email createdAt gender barangay')
      .sort({ createdAt: -1 });

    res.json({
      totalCount: users.length,
      dateRange: {
        start: start.toISOString(),
        end: new Date(endDate).toISOString() // Original end date without the +1 day
      },
      users
    });
  } catch (error) {
    console.error('Registration report error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get system health information
exports.getSystemHealth = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const os = require('os');
    const mongoose = require('mongoose');
    const User = require('../models/User');

    // Calculate server uptime
    const uptime = os.uptime();
    const startTime = new Date(Date.now() - (uptime * 1000));

    // Get memory usage
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsagePercent = (usedMemory / totalMemory) * 100;

    // Get CPU information
    const cpuCores = os.cpus().length;

    // Calculate CPU usage (this is a simple approximation)
    const cpuUsage = process.cpuUsage();
    const cpuUsagePercent = ((cpuUsage.user + cpuUsage.system) / 1000000) * 5; // Rough estimate

    // Check database connection
    const dbConnected = mongoose.connection.readyState === 1;

    // Measure database response time
    const dbStartTime = Date.now();
    await User.findOne().limit(1).select('_id');
    const dbResponseTime = Date.now() - dbStartTime;

    // Get active users count
    const now = new Date();
    const oneDayAgo = new Date(now);
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);

    const activeUsersCount = await User.countDocuments({ lastLogin: { $gte: oneDayAgo } });
    const totalUsersCount = await User.countDocuments();

    // API health check
    const apiStatus = 'healthy'; // You can implement more sophisticated checks
    const apiResponseTime = 10; // Placeholder value

    res.json({
      uptime,
      startTime,
      memory: {
        total: totalMemory,
        free: freeMemory,
        used: usedMemory,
        usedPercentage: memoryUsagePercent
      },
      cpu: {
        cores: cpuCores,
        usage: Math.min(cpuUsagePercent, 100) // Cap at 100%
      },
      database: {
        connected: dbConnected,
        responseTime: dbResponseTime
      },
      api: {
        status: apiStatus,
        responseTime: apiResponseTime
      },
      activeUsers: {
        count: activeUsersCount,
        last24Hours: activeUsersCount,
        total: totalUsersCount
      }
    });
  } catch (error) {
    console.error('System health error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Create new user (admin only)
exports.createUser = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const { username, email, password, firstName, lastName, dateOfBirth, gender, barangay, isAdmin, adminRole } = req.body;

    // Validate required fields
    if (!username || !email || !password) {
      return res.status(400).json({
        message: 'Username, email and password are required'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ $or: [{ email }, { username }] });
    if (existingUser) {
      return res.status(400).json({
        message: 'User already exists with that email or username'
      });
    }

    // Create new user
    const user = new User({
      username,
      email,
      password,
      firstName,
      lastName,
      dateOfBirth,
      gender,
      barangay,
      isAdmin: isAdmin || false
    });

    await user.save();

    // If creating an admin user, also create Admin record
    if (isAdmin) {
      let permissions = [];
      let role = adminRole || 'admin';

      // Set permissions based on role
      if (role === 'super_admin') {
        permissions = ['user_management', 'analytics_view', 'system_health', 'reports_generate', 'content_management', 'meal_management', 'feedback_management'];
      } else if (role === 'admin') {
        permissions = ['user_management', 'analytics_view', 'system_health', 'meal_management', 'feedback_management'];
      } else if (role === 'sub_admin') {
        permissions = ['meal_management', 'feedback_management'];
      } else if (role === 'moderator') {
        permissions = ['content_management'];
      }

      const adminRecord = new Admin({
        user: user._id,
        role: role,
        permissions: permissions,
        createdBy: req.user.id
      });
      await adminRecord.save();
    }

    // Return user info without password
    const userResponse = user.toObject();
    delete userResponse.password;

    res.status(201).json({
      message: 'User created successfully',
      user: userResponse
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Update user (admin only)
exports.updateUser = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const { userId } = req.params;
    const updateData = req.body;

    // Don't allow updating password through this endpoint
    if (updateData.password) {
      delete updateData.password;
    }

    // Find and update user
    const user = await User.findByIdAndUpdate(
      userId,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      message: 'User updated successfully',
      user
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Disable user (admin only)
exports.disableUser = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const { userId } = req.params;

    // Prevent admin from disabling themselves
    if (userId === req.user.id) {
      return res.status(400).json({ message: 'Cannot disable your own account' });
    }

    // Find and update user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is already disabled
    if (!user.isActive) {
      return res.status(400).json({ message: 'User is already disabled' });
    }

    // Disable the user
    user.isActive = false;
    user.disabledAt = new Date();
    user.disabledBy = req.user.id;
    await user.save();

    // Log the activity
    await ActivityService.logActivity(req.user.id, 'disabled_account', {
      targetUser: user.email,
      targetUsername: user.username,
      adminAction: true
    }, req);

    res.json({
      message: 'User disabled successfully',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        isActive: user.isActive,
        disabledAt: user.disabledAt
      }
    });
  } catch (error) {
    console.error('Disable user error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Enable user (admin only)
exports.enableUser = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const { userId } = req.params;

    // Find and update user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is already enabled
    if (user.isActive) {
      return res.status(400).json({ message: 'User is already enabled' });
    }

    // Enable the user
    user.isActive = true;
    user.disabledAt = undefined;
    user.disabledBy = undefined;
    await user.save();

    // Log the activity
    await ActivityService.logActivity(req.user.id, 'enabled_account', {
      targetUser: user.email,
      targetUsername: user.username,
      adminAction: true
    }, req);

    res.json({
      message: 'User enabled successfully',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error('Enable user error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Make user admin (admin only)
exports.makeUserAdmin = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const { userId } = req.params;
    const { adminRole } = req.body; // Get role from request body

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is already an admin
    if (user.isAdmin) {
      return res.status(400).json({ message: 'User is already an admin' });
    }

    // Update user to be admin
    user.isAdmin = true;
    await user.save();

    // Set permissions based on role
    let permissions = [];
    let role = adminRole || 'admin';

    if (role === 'super_admin') {
      permissions = ['user_management', 'analytics_view', 'system_health', 'reports_generate', 'content_management', 'meal_management', 'feedback_management'];
    } else if (role === 'admin') {
      permissions = ['user_management', 'analytics_view', 'system_health', 'meal_management', 'feedback_management'];
    } else if (role === 'sub_admin') {
      permissions = ['meal_management', 'feedback_management'];
    } else if (role === 'moderator') {
      permissions = ['content_management'];
    }

    // Create Admin record
    const adminRecord = new Admin({
      user: user._id,
      role: role,
      permissions: permissions,
      createdBy: req.user.id
    });
    await adminRecord.save();

    res.json({
      message: 'User promoted to admin successfully',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        isAdmin: user.isAdmin
      }
    });
  } catch (error) {
    console.error('Make user admin error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Remove user admin privileges (admin only)
exports.removeUserAdmin = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const { userId } = req.params;

    // Prevent admin from removing their own admin privileges
    if (userId === req.user.id) {
      return res.status(400).json({ message: 'Cannot remove your own admin privileges' });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is not an admin
    if (!user.isAdmin) {
      return res.status(400).json({ message: 'User is not an admin' });
    }

    // Update user to remove admin privileges
    user.isAdmin = false;
    await user.save();

    // Remove Admin record
    await Admin.findOneAndDelete({ user: userId });

    res.json({
      message: 'Admin privileges removed successfully',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        isAdmin: user.isAdmin
      }
    });
  } catch (error) {
    console.error('Remove user admin error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get geolocation analytics (admin only)
exports.getGeolocationAnalytics = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    // Get verified user distribution by barangay
    const barangayStats = await User.aggregate([
      {
        $match: {
          barangay: { $exists: true, $ne: null, $ne: '' },
          isEmailVerified: true
        }
      },
      {
        $group: {
          _id: '$barangay',
          userCount: { $sum: 1 },
          users: {
            $push: {
              id: '$_id',
              username: '$username',
              email: '$email',
              createdAt: '$createdAt'
            }
          }
        }
      },
      {
        $sort: { userCount: -1 }
      }
    ]);

    // Get total verified users with and without barangay info
    const totalUsers = await User.countDocuments({ isEmailVerified: true });
    const usersWithBarangay = await User.countDocuments({
      barangay: { $exists: true, $ne: null, $ne: '' },
      isEmailVerified: true
    });
    const usersWithoutBarangay = totalUsers - usersWithBarangay;

    // Get recent verified signups by barangay (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentSignupsByBarangay = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo },
          barangay: { $exists: true, $ne: null, $ne: '' },
          isEmailVerified: true
        }
      },
      {
        $group: {
          _id: '$barangay',
          recentSignups: { $sum: 1 }
        }
      },
      {
        $sort: { recentSignups: -1 }
      }
    ]);

    res.json({
      totalUsers,
      usersWithBarangay,
      usersWithoutBarangay,
      barangayDistribution: barangayStats,
      recentSignupsByBarangay,
      dataCollectedAt: new Date()
    });
  } catch (error) {
    console.error('Geolocation analytics error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Update admin role (admin only)
exports.updateAdminRole = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const { userId } = req.params;
    const { adminRole } = req.body;

    // Validate role
    const validRoles = ['super_admin', 'admin', 'sub_admin', 'moderator'];
    if (!validRoles.includes(adminRole)) {
      return res.status(400).json({ message: 'Invalid admin role' });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is admin
    if (!user.isAdmin) {
      return res.status(400).json({ message: 'User is not an admin' });
    }

    // Find admin record
    const adminRecord = await Admin.findOne({ user: userId });
    if (!adminRecord) {
      return res.status(404).json({ message: 'Admin record not found' });
    }

    // Set permissions based on role
    let permissions = [];
    if (adminRole === 'super_admin') {
      permissions = ['user_management', 'analytics_view', 'system_health', 'reports_generate', 'content_management', 'meal_management', 'feedback_management'];
    } else if (adminRole === 'admin') {
      permissions = ['user_management', 'analytics_view', 'system_health', 'meal_management', 'feedback_management'];
    } else if (adminRole === 'sub_admin') {
      permissions = ['meal_management', 'feedback_management'];
    } else if (adminRole === 'moderator') {
      permissions = ['content_management'];
    }

    // Update admin record
    adminRecord.role = adminRole;
    adminRecord.permissions = permissions;
    await adminRecord.save();

    res.json({
      message: 'Admin role updated successfully',
      role: adminRole,
      permissions: permissions
    });
  } catch (error) {
    console.error('Update admin role error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get admin info for a user (admin only)
exports.getAdminInfo = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const { userId } = req.params;

    // Find admin record
    const adminRecord = await Admin.findOne({ user: userId, isActive: true });
    if (!adminRecord) {
      return res.status(404).json({ message: 'Admin record not found' });
    }

    res.json({
      role: adminRecord.role,
      permissions: adminRecord.permissions,
      createdAt: adminRecord.createdAt,
      lastActivity: adminRecord.lastActivity
    });
  } catch (error) {
    console.error('Get admin info error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};
